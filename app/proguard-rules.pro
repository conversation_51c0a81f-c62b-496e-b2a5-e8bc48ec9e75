# SavePro – Status Downloader - Memory-Optimized ProGuard Rules for 2025
# Optimized for low-end devices and efficient video/media handling

# Keep line numbers for crash reporting
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Keep all application classes (essential for functionality)
-keep class com.ks.app.service.statussaver.** { *; }

# Keep model classes (for data integrity and serialization)
-keep class com.ks.app.service.statussaver.data.models.** { *; }

# Keep ViewModels and their methods (memory-efficient lifecycle management)
-keep class * extends androidx.lifecycle.ViewModel {
    <init>(...);
}
-keep class * extends androidx.lifecycle.AndroidViewModel {
    <init>(...);
}

# Keep Fragment constructors (essential for fragment recreation)
-keepclassmembers class * extends androidx.fragment.app.Fragment {
    public <init>(...);
}

# Memory optimization: Keep only essential Activity methods
-keepclassmembers class * extends androidx.appcompat.app.AppCompatActivity {
    public <init>(...);
    protected void onCreate(android.os.Bundle);
    protected void onDestroy();
    protected void onPause();
    protected void onResume();
}

# Keep Activity constructors
-keepclassmembers class * extends androidx.appcompat.app.AppCompatActivity {
    public <init>(...);
}

# Glide optimizations
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
    <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
    *** rewind();
}

# Media3 (ExoPlayer) optimizations for memory-efficient video playback - Android 15 (API 35) latest
-keep class androidx.media3.** { *; }
-dontwarn androidx.media3.**
-keep class androidx.media3.ui.** { *; }
-keep class androidx.media3.exoplayer.** { *; }
-keep class androidx.media3.common.** { *; }

# Glide 5.0 optimizations for Android 15
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule { <init>(...); }
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}

# Google Mobile Ads SDK (AdMob) optimizations
-keep class com.google.android.gms.ads.** { *; }
-keep class com.google.ads.** { *; }
-keep class com.google.android.gms.common.** { *; }
-dontwarn com.google.android.gms.ads.**
-dontwarn com.google.ads.**

# PhotoView library (memory-efficient image viewing)
-keep class com.github.chrisbanes.photoview.** { *; }

# Shimmer library (optimized loading animations)
-keep class com.facebook.shimmer.** { *; }

# Video playback optimization - Keep VideoView and MediaController
-keep class android.widget.VideoView { *; }
-keep class android.widget.MediaController { *; }
-keep class android.media.MediaPlayer { *; }
-keep class android.media.MediaPlayer$* { *; }

# Memory management - Keep essential lifecycle components only
-keep class androidx.lifecycle.ViewModel { *; }
-keep class androidx.lifecycle.LiveData { *; }
-keep class androidx.lifecycle.MutableLiveData { *; }

# UI components - Keep only what's used
-keep class com.google.android.material.tabs.TabLayout { *; }
-keep class com.google.android.material.bottomsheet.** { *; }
-keep class androidx.viewpager2.widget.ViewPager2 { *; }
-keep class androidx.recyclerview.widget.RecyclerView { *; }
-keep class androidx.recyclerview.widget.GridLayoutManager { *; }
-keep class androidx.swiperefreshlayout.widget.SwipeRefreshLayout { *; }



# DocumentFile for SAF (Storage Access Framework) - Essential for file access
-keep class androidx.documentfile.provider.DocumentFile { *; }



# Keep animation-related classes for finger pointing animation
-keep class android.animation.ObjectAnimator { *; }
-keep class android.animation.ValueAnimator { *; }
-keep class android.animation.AnimatorSet { *; }

# Memory optimization: Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Optimization settings
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep custom views (memory-optimized - only essential methods)
-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Memory optimization: Keep only essential reflection-based classes
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep Serializable classes
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep enums
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Memory optimization for low-end devices
-optimizations !code/simplification/variable,!code/simplification/arithmetic
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Aggressive optimization for memory efficiency
-repackageclasses ''
-allowaccessmodification
-mergeinterfacesaggressively

# Remove debug information in release
-printmapping mapping.txt
-printseeds seeds.txt
-printusage usage.txt

# Additional memory optimizations for video/media handling
-assumenosideeffects class java.lang.System {
    public static long currentTimeMillis();
    static java.lang.Class getCallerClass();
    public static int identityHashCode(java.lang.Object);
    public static java.lang.SecurityManager getSecurityManager();
    public static java.util.Properties getProperties();
    public static java.lang.String getProperty(java.lang.String);
    public static java.lang.String getenv(java.lang.String);
    public static java.lang.String mapLibraryName(java.lang.String);
    public static java.lang.String getProperty(java.lang.String,java.lang.String);
}

# Optimize string operations for memory efficiency
-assumenosideeffects class java.lang.String {
    public java.lang.String();
    public java.lang.String(byte[]);
    public java.lang.String(byte[],int);
    public java.lang.String(byte[],int,int);
    public java.lang.String(byte[],int,int,int);
    public java.lang.String(byte[],int,int,java.lang.String);
    public java.lang.String(byte[],java.lang.String);
    public java.lang.String(char[]);
    public java.lang.String(char[],int,int);
    public java.lang.String(java.lang.String);
    public java.lang.String(java.lang.StringBuffer);
}
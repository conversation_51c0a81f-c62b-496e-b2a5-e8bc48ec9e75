package com.ks.app.service.statussaver;

import android.app.Application;
import android.util.Log;
import com.google.android.gms.ads.MobileAds;
import com.ks.app.service.statussaver.utils.GlobalUpdateManager;


/**
 * ✅ SavePro Application Class
 * Handles app-wide initialization including AdMob SDK
 */
public class SaveProApplication extends Application {

    private static final String TAG = "SaveProApplication";

    @Override
    public void onCreate() {
        super.onCreate();

        Log.d(TAG, "SavePro – Status Downloader application starting...");

        // ✅ Initialize Global Update Manager FIRST
        initializeGlobalUpdateManager();

        // ✅ Initialize AdMob SDK
        initializeAdMob();

        Log.d(TAG, "SavePro application initialization complete");
    }

    /**
     * ✅ Initialize Global Update Manager
     * Handles mandatory in-app updates globally across the entire app
     */
    private void initializeGlobalUpdateManager() {
        try {
            Log.d(TAG, "🚀 Initializing Global Update Manager...");

            GlobalUpdateManager.getInstance().initialize(this);

            Log.d(TAG, "✅ Global Update Manager initialized successfully");
            Log.d(TAG, "🛡️ App-wide update protection enabled");

        } catch (Exception e) {
            Log.e(TAG, "❌ Error initializing Global Update Manager: " + e.getMessage(), e);
        }
    }
    
    /**
     * ✅ Initialize AdMob SDK - Optimized for Background Loading
     * Early initialization to enable fast ad loading during splash screen
     */
    private void initializeAdMob() {
        try {
            Log.d(TAG, "🚀 Early AdMob SDK initialization for background loading...");

            // Initialize AdMob with production configuration early
            MobileAds.initialize(this, initializationStatus -> {
                Log.d(TAG, "✅ AdMob SDK initialized successfully");
            });

            // Production mode - test mode disabled for live ads
            Log.d(TAG, "✅ AdMob initialized for production with App ID: ca-app-pub-7557152164205920~6417975169");
            Log.d(TAG, "⚡ SDK ready for background ad loading during splash screen");

            Log.d(TAG, "AdMob SDK initialization completed - ready for fast ad loading");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing AdMob SDK: " + e.getMessage(), e);
        }
    }
}

package com.ks.app.service.statussaver;

import android.app.Application;
import android.util.Log;
import com.google.android.gms.ads.MobileAds;
import com.ks.app.service.statussaver.utils.GlobalUpdateManager;
import com.ks.app.service.statussaver.utils.BackgroundAdManager;


/**
 * ✅ SavePro Application Class
 * Handles app-wide initialization including AdMob SDK
 */
public class SaveProApplication extends Application {

    private static final String TAG = "SaveProApplication";

    @Override
    public void onCreate() {
        super.onCreate();

        Log.d(TAG, "SavePro – Status Downloader application starting...");

        // ✅ Initialize Global Update Manager FIRST
        initializeGlobalUpdateManager();

        // ✅ Initialize Background Ad Manager for efficient ad loading
        initializeBackgroundAdManager();

        // ✅ Initialize AdMob SDK (will be handled by BackgroundAdManager)
        // initializeAdMob(); // Removed - handled by BackgroundAdManager

        Log.d(TAG, "SavePro application initialization complete");
    }

    /**
     * ✅ Initialize Global Update Manager
     * Handles mandatory in-app updates globally across the entire app
     */
    private void initializeGlobalUpdateManager() {
        try {
            Log.d(TAG, "🚀 Initializing Global Update Manager...");

            GlobalUpdateManager.getInstance().initialize(this);

            Log.d(TAG, "✅ Global Update Manager initialized successfully");
            Log.d(TAG, "🛡️ App-wide update protection enabled");

        } catch (Exception e) {
            Log.e(TAG, "❌ Error initializing Global Update Manager: " + e.getMessage(), e);
        }
    }

    /**
     * ✅ Initialize Background Ad Manager
     * Handles efficient background ad loading with caching and retry mechanism
     */
    private void initializeBackgroundAdManager() {
        try {
            Log.d(TAG, "🚀 Initializing Background Ad Manager...");

            BackgroundAdManager.getInstance().initialize(this);

            Log.d(TAG, "✅ Background Ad Manager initialized successfully");
            Log.d(TAG, "📦 Ad caching and background loading enabled");
            Log.d(TAG, "🎯 Production Ad Units:");
            Log.d(TAG, "   📱 App ID: ca-app-pub-7557152164205920~6417975169");
            Log.d(TAG, "   📺 Interstitial: ca-app-pub-7557152164205920/6418571631");

        } catch (Exception e) {
            Log.e(TAG, "❌ Error initializing Background Ad Manager: " + e.getMessage(), e);
        }
    }
}

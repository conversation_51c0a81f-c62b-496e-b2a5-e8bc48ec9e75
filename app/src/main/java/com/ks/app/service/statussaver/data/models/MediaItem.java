package com.ks.app.service.statussaver.data.models;

import android.net.Uri;

public class MediaItem {
    private final Uri uri;
    private final String fileName;
    private final boolean isVideo;
    private final long dateModified; // For sorting or other purposes

    public MediaItem(Uri uri, String fileName, boolean isVideo, long dateModified) {
        this.uri = uri;
        this.fileName = fileName != null ? fileName : "unknown";
        this.isVideo = isVideo;
        this.dateModified = dateModified;
    }

    public Uri getUri() {
        return uri;
    }

    public String getFileName() {
        return fileName != null ? fileName : "";
    }

    public boolean isVideo() {
        return isVideo;
    }

    public long getDateModified() {
        return dateModified;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        MediaItem mediaItem = (MediaItem) obj;

        if (isVideo != mediaItem.isVideo) return false;
        if (dateModified != mediaItem.dateModified) return false;
        if (uri != null ? !uri.equals(mediaItem.uri) : mediaItem.uri != null) return false;
        return fileName != null ? fileName.equals(mediaItem.fileName) : mediaItem.fileName == null;
    }

    @Override
    public int hashCode() {
        int result = uri != null ? uri.hashCode() : 0;
        result = 31 * result + (fileName != null ? fileName.hashCode() : 0);
        result = 31 * result + (isVideo ? 1 : 0);
        result = 31 * result + (int) (dateModified ^ (dateModified >>> 32));
        return result;
    }

    /**
     * Null-safe string comparison for fileName
     */
    public boolean fileNameEquals(String other) {
        if (fileName == null && other == null) return true;
        if (fileName == null || other == null) return false;
        return fileName.equals(other);
    }
}

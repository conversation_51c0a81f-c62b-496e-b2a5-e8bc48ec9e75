package com.ks.app.service.statussaver.data.repository;

import android.content.Context;
import android.util.Log;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.data.models.MediaItem;
import com.ks.app.service.statussaver.utils.MediaStoreUtils;
import com.ks.app.service.statussaver.utils.SAFUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Repository class for managing media data efficiently
 * Handles data loading from various sources with optimized performance
 */
public class MediaRepository {

    private static final String TAG = "MediaRepository";
    private final Context context;
    private final ExecutorService executorService;

    // LiveData for loading states
    private final MutableLiveData<Boolean> isLoadingImages = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> isLoadingVideos = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> isLoadingSaved = new MutableLiveData<>(false);
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();

    // LiveData for actual data
    private final MutableLiveData<List<MediaItem>> imageStatuses = new MutableLiveData<>();
    private final MutableLiveData<List<MediaItem>> videoStatuses = new MutableLiveData<>();
    private final MutableLiveData<List<MediaItem>> savedMedia = new MutableLiveData<>();
    
    public MediaRepository(Context context) {
        this.context = context.getApplicationContext();
        this.executorService = Executors.newFixedThreadPool(3);
    }
    
    /**
     * Get image statuses LiveData
     */
    public LiveData<List<MediaItem>> getImageStatuses() {
        return imageStatuses;
    }

    /**
     * Get video statuses LiveData
     */
    public LiveData<List<MediaItem>> getVideoStatuses() {
        return videoStatuses;
    }

    /**
     * Get saved media LiveData
     */
    public LiveData<List<MediaItem>> getSavedMedia() {
        return savedMedia;
    }
    
    /**
     * ✅ Load WhatsApp statuses for Images/Videos tabs
     * This uses folder access (SAF/File) NOT media permissions
     * Images/Videos tabs should load immediately without waiting for media permissions
     */
    public void loadWhatsAppStatuses() {
        isLoadingImages.setValue(true);
        isLoadingVideos.setValue(true);

        executorService.execute(() -> {
            try {
                Log.d(TAG, "Loading Images/Videos tabs (uses folder access, NOT media permissions)");

                // ✅ Uses folder access - independent of media permissions
                List<MediaItem> allStatuses = SAFUtils.getWhatsAppStatuses(context);

                // Sort by date modified (newest first)
                Collections.sort(allStatuses, (o1, o2) -> Long.compare(o2.getDateModified(), o1.getDateModified()));

                // Separate into images and videos
                List<MediaItem> images = new ArrayList<>();
                List<MediaItem> videos = new ArrayList<>();

                for (MediaItem item : allStatuses) {
                    if (item.isVideo()) {
                        videos.add(item);
                    } else {
                        images.add(item);
                    }
                }

                // Update LiveData (will be empty if no permissions, but that's OK)
                imageStatuses.postValue(images);
                videoStatuses.postValue(videos);

                if (allStatuses.isEmpty()) {
                    Log.d(TAG, "No statuses loaded - likely due to missing permissions or no data");
                } else {
                    Log.d(TAG, "Loaded " + images.size() + " images and " + videos.size() + " videos");
                }

            } catch (Exception e) {
                Log.e(TAG, "Error loading WhatsApp statuses", e);
                // Don't show error message for permission issues - just log it
                Log.d(TAG, "Loading failed, likely due to permissions - will retry when permissions are granted");

                // Post empty lists so UI shows empty state instead of loading forever
                imageStatuses.postValue(new ArrayList<>());
                videoStatuses.postValue(new ArrayList<>());
            } finally {
                isLoadingImages.postValue(false);
                isLoadingVideos.postValue(false);
            }
        });
    }
    
    /**
     * ✅ Load saved media for Saved tab
     * This REQUIRES media permissions (READ_EXTERNAL_STORAGE/READ_MEDIA_*)
     * Will be empty if media permissions are not granted
     */
    public void loadSavedMedia() {
        // Don't start new load if already loading
        Boolean currentlyLoading = isLoadingSaved.getValue();
        if (currentlyLoading != null && currentlyLoading) {
            Log.d(TAG, "Already loading saved media, skipping duplicate request");
            return;
        }

        isLoadingSaved.setValue(true);

        executorService.execute(() -> {
            try {
                Log.d(TAG, "Loading Saved tab (requires media permissions)");

                List<MediaItem> savedMediaList = MediaStoreUtils.getAllGalleryMedia(context);

                if (savedMediaList == null) {
                    savedMediaList = new ArrayList<>();
                    Log.w(TAG, "MediaStoreUtils returned null, using empty list");
                }

                // Filter out any invalid items
                List<MediaItem> validItems = new ArrayList<>();
                for (MediaItem item : savedMediaList) {
                    if (item != null && item.getUri() != null && item.getFileName() != null) {
                        validItems.add(item);
                    }
                }

                savedMedia.postValue(validItems);
                Log.d(TAG, "Successfully loaded " + validItems.size() + " saved media items");

            } catch (SecurityException e) {
                Log.d(TAG, "Security exception loading saved media - permission issue (expected without permissions)", e);
                // Don't show error message for permission issues - just log it
                savedMedia.postValue(new ArrayList<>());
            } catch (Exception e) {
                Log.e(TAG, "Error loading saved media", e);
                // Don't show error message for permission issues - just log it
                Log.d(TAG, "Loading saved media failed, likely due to permissions - will retry when permissions are granted");
                savedMedia.postValue(new ArrayList<>());
            } finally {
                isLoadingSaved.postValue(false);
            }
        });
    }
    
    // Getters for loading states
    public LiveData<Boolean> getIsLoadingImages() { return isLoadingImages; }
    public LiveData<Boolean> getIsLoadingVideos() { return isLoadingVideos; }
    public LiveData<Boolean> getIsLoadingSaved() { return isLoadingSaved; }
    public LiveData<String> getErrorMessage() { return errorMessage; }
    
    public void clearErrorMessage() {
        errorMessage.setValue(null);
    }
    
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}

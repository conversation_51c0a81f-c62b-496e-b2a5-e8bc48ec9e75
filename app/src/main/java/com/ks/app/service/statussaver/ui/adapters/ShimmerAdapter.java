package com.ks.app.service.statussaver.ui.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.ks.app.service.statussaver.R;

/**
 * Optimized adapter for showing shimmer skeleton loading items
 * Implements Facebook Shimmer best practices for performance
 */
public class ShimmerAdapter extends RecyclerView.Adapter<ShimmerAdapter.ShimmerViewHolder> {

    private static final int SHIMMER_ITEM_COUNT = 12; // Show 12 shimmer items (6 rows x 2 columns)

    @NonNull
    @Override
    public ShimmerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.shimmer_media_item, parent, false);
        return new ShimmerViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ShimmerViewHolder holder, int position) {
        // No binding needed for shimmer items - optimized for performance
        // Avoid any unnecessary operations in bind to reduce overdraw
    }

    @Override
    public int getItemCount() {
        return SHIMMER_ITEM_COUNT;
    }

    @Override
    public long getItemId(int position) {
        // Return stable IDs for better recycling performance
        return position;
    }

    @Override
    public int getItemViewType(int position) {
        // Single view type for all shimmer items - optimized recycling
        return 0;
    }

    static class ShimmerViewHolder extends RecyclerView.ViewHolder {
        public ShimmerViewHolder(@NonNull View itemView) {
            super(itemView);
            // Pre-optimize view for better performance
            itemView.setHasTransientState(false);
        }
    }
}

package com.ks.app.service.statussaver.ui.ads;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.ks.app.service.statussaver.R;

public class InterstitialAdActivity extends AppCompatActivity {

    private static final String TAG = "InterstitialAdActivity";
    private static final String AD_UNIT_ID = "ca-app-pub-7557152164205920/6418571631";
    private static final int LOADING_DURATION = 3000; // 3 seconds
    
    // UI Components
    private ProgressBar progressBar;
    private TextView loadingText;
    private View loadingContainer;
    
    // Ad Components
    private InterstitialAd interstitialAd;
    private boolean isAdLoaded = false;
    private boolean isLoadingComplete = false;
    
    // Navigation
    private String nextActivity;
    private Bundle nextActivityExtras;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Setup full screen and status bar
        setupFullScreen();
        setStatusBarColor();
        
        setContentView(R.layout.activity_interstitial_ad);
        
        // Get navigation parameters
        getNavigationParameters();
        
        // Initialize UI
        initializeUI();
        
        // Initialize AdMob
        initializeAdMob();
        
        // Start loading process
        startLoadingProcess();
    }
    
    /**
     * Setup full screen experience
     */
    private void setupFullScreen() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            getWindow().setDecorFitsSystemWindows(false);
        } else {
            getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            );
        }
    }
    
    /**
     * Set status bar color
     */
    private void setStatusBarColor() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(ContextCompat.getColor(this, android.R.color.black));
        }
    }
    
    /**
     * Get navigation parameters from intent
     */
    private void getNavigationParameters() {
        Intent intent = getIntent();
        nextActivity = intent.getStringExtra("next_activity");
        nextActivityExtras = intent.getBundleExtra("next_activity_extras");
        
        if (nextActivity == null) {
            nextActivity = "MainActivity"; // Default fallback
        }
        
        Log.d(TAG, "🎯 NAVIGATION TARGET: " + nextActivity);
    }
    
    /**
     * Initialize UI components
     */
    private void initializeUI() {
        loadingContainer = findViewById(R.id.loading_container);
        progressBar = findViewById(R.id.progress_bar);
        loadingText = findViewById(R.id.loading_text);
        
        // Start with fade in animation
        AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
        fadeIn.setDuration(500);
        loadingContainer.startAnimation(fadeIn);
        
        Log.d(TAG, "🎨 UI INITIALIZED: Loading screen ready");
    }
    
    /**
     * Initialize AdMob SDK
     */
    private void initializeAdMob() {
        MobileAds.initialize(this, initializationStatus -> {
            Log.d(TAG, "🚀 ADMOB INITIALIZED: Ready to load ads");
            loadInterstitialAd();
        });
    }
    
    /**
     * Load interstitial ad
     */
    private void loadInterstitialAd() {
        AdRequest adRequest = new AdRequest.Builder().build();

        InterstitialAd.load(this, AD_UNIT_ID, adRequest, new InterstitialAdLoadCallback() {
            @Override
            public void onAdLoaded(@NonNull InterstitialAd ad) {
                Log.d(TAG, "✅ AD LOADED: Interstitial ad ready to show");
                interstitialAd = ad;
                isAdLoaded = true;
                setupAdCallbacks();
                checkIfReadyToShowAd();
            }

            @Override
            public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                Log.e(TAG, "❌ AD LOAD FAILED: " + loadAdError.getMessage());
                interstitialAd = null;
                isAdLoaded = false;
                // Continue without ad after loading time
            }
        });
    }

    /**
     * Setup ad callbacks for smooth transitions
     */
    private void setupAdCallbacks() {
        if (interstitialAd != null) {
            interstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                @Override
                public void onAdClicked() {
                    Log.d(TAG, "👆 AD CLICKED: User interacted with ad");
                }

                @Override
                public void onAdDismissedFullScreenContent() {
                    Log.d(TAG, "✅ AD DISMISSED: User closed ad, navigating to next screen");
                    interstitialAd = null;
                    navigateToNextActivity();
                }

                @Override
                public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                    Log.e(TAG, "❌ AD SHOW FAILED: " + adError.getMessage());
                    interstitialAd = null;
                    navigateToNextActivity();
                }

                @Override
                public void onAdImpression() {
                    Log.d(TAG, "👁️ AD IMPRESSION: Ad displayed successfully");
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    Log.d(TAG, "📺 AD SHOWED: Full screen ad displayed");
                }
            });
        }
    }

    /**
     * Start the loading process with timer
     */
    private void startLoadingProcess() {
        Log.d(TAG, "⏱️ LOADING STARTED: 3 second timer begins");

        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            Log.d(TAG, "⏰ LOADING COMPLETE: 3 seconds elapsed");
            isLoadingComplete = true;
            checkIfReadyToShowAd();
        }, LOADING_DURATION);
    }

    /**
     * Check if ready to show ad (both loading complete and ad loaded)
     */
    private void checkIfReadyToShowAd() {
        if (isLoadingComplete) {
            if (isAdLoaded && interstitialAd != null) {
                showAdWithSmoothTransition();
            } else {
                // No ad available, navigate directly
                Log.d(TAG, "⚠️ NO AD AVAILABLE: Navigating directly to next screen");
                navigateToNextActivity();
            }
        }
    }

    /**
     * Show ad with smooth slide transition
     */
    private void showAdWithSmoothTransition() {
        Log.d(TAG, "🎬 SHOWING AD: Starting smooth transition");

        // Slide out loading screen
        TranslateAnimation slideOut = new TranslateAnimation(0, -getResources().getDisplayMetrics().widthPixels, 0, 0);
        slideOut.setDuration(500);
        slideOut.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                Log.d(TAG, "🎭 SLIDE OUT STARTED: Loading screen sliding out");
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                Log.d(TAG, "🎭 SLIDE OUT COMPLETE: Showing interstitial ad");
                loadingContainer.setVisibility(View.GONE);

                // Show the ad
                if (interstitialAd != null) {
                    interstitialAd.show(InterstitialAdActivity.this);
                } else {
                    navigateToNextActivity();
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {}
        });

        loadingContainer.startAnimation(slideOut);
    }

    /**
     * Navigate to next activity with smooth transition
     */
    private void navigateToNextActivity() {
        Log.d(TAG, "🚀 NAVIGATING: Moving to " + nextActivity);

        try {
            Intent intent;

            switch (nextActivity) {
                case "MainActivity":
                    intent = new Intent(this, com.ks.app.service.statussaver.ui.main.MainActivity.class);
                    break;
                case "SafPermissionActivity":
                    intent = new Intent(this, com.ks.app.service.statussaver.ui.main.SafPermissionActivity.class);
                    break;
                default:
                    intent = new Intent(this, com.ks.app.service.statussaver.ui.main.MainActivity.class);
                    break;
            }

            // Add extras if provided
            if (nextActivityExtras != null) {
                intent.putExtras(nextActivityExtras);
            }

            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            startActivity(intent);

            // Smooth transition
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
            finish();

        } catch (Exception e) {
            Log.e(TAG, "❌ NAVIGATION ERROR: " + e.getMessage(), e);
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (interstitialAd != null) {
            interstitialAd = null;
        }
        Log.d(TAG, "🧹 ACTIVITY DESTROYED: Cleaned up resources");
    }

    @Override
    public void onBackPressed() {
        // Prevent back button during ad loading/showing
        Log.d(TAG, "🔙 BACK PRESSED: Ignored during ad process");
    }
}

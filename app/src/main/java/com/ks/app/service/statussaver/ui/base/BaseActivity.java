package com.ks.app.service.statussaver.ui.base;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.appcompat.app.AppCompatActivity;

import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.utils.GlobalUpdateManager;

/**
 * ✅ Base Activity - Global Update Protection
 * ALL activities in the app must extend this class
 * Provides global in-app update blocking functionality
 * Ensures no screen can be used without updating when required
 */
public abstract class BaseActivity extends AppCompatActivity {
    
    private static final String TAG = "BaseActivity";
    
    // Update blocking state
    private boolean isBlocked = false;
    private View blockingOverlay;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        Log.d(TAG, "🚀 ACTIVITY CREATED: " + this.getClass().getSimpleName());
        
        // CRITICAL: Check for global update block FIRST
        checkGlobalUpdateBlock();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        Log.d(TAG, "▶️ ACTIVITY RESUMED: " + this.getClass().getSimpleName());
        
        // Set current activity in GlobalUpdateManager
        GlobalUpdateManager.getInstance().setCurrentActivity(this);
        
        // CRITICAL: Check for global update block on EVERY resume
        checkGlobalUpdateBlock();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        
        Log.d(TAG, "⏸️ ACTIVITY PAUSED: " + this.getClass().getSimpleName());
        
        // Clear current activity reference
        GlobalUpdateManager.getInstance().clearCurrentActivity();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        Log.d(TAG, "💀 ACTIVITY DESTROYED: " + this.getClass().getSimpleName());
        
        // Clean up blocking overlay
        if (blockingOverlay != null) {
            removeBlockingOverlay();
        }
    }
    
    /**
     * ✅ Check Global Update Block
     * Determines if this activity should be blocked due to required update
     */
    private void checkGlobalUpdateBlock() {
        boolean shouldBlock = GlobalUpdateManager.getInstance().isAppBlocked();
        
        Log.d(TAG, "🔍 UPDATE BLOCK CHECK: Should block = " + shouldBlock + 
                   " for " + this.getClass().getSimpleName());
        
        if (shouldBlock && !isBlocked) {
            // Block this activity
            blockActivity();
        } else if (!shouldBlock && isBlocked) {
            // Unblock this activity
            unblockActivity();
        }
    }
    
    /**
     * ✅ Block Activity
     * Completely blocks user interaction and shows update requirement
     */
    private void blockActivity() {
        Log.w(TAG, "🛑 BLOCKING ACTIVITY: " + this.getClass().getSimpleName());
        
        isBlocked = true;
        
        // Create full-screen blocking overlay
        createBlockingOverlay();
        
        // Disable all touch events
        disableUserInteraction();
        
        // Notify GlobalUpdateManager to handle the block
        GlobalUpdateManager.getInstance().handleActivityBlock(this);
    }
    
    /**
     * ✅ Unblock Activity
     * Removes blocking and restores normal functionality
     */
    private void unblockActivity() {
        Log.d(TAG, "✅ UNBLOCKING ACTIVITY: " + this.getClass().getSimpleName());
        
        isBlocked = false;
        
        // Remove blocking overlay
        removeBlockingOverlay();
        
        // Re-enable user interaction
        enableUserInteraction();
    }
    
    /**
     * ✅ Create Blocking Overlay
     * Creates a full-screen overlay that covers all content
     */
    private void createBlockingOverlay() {
        if (blockingOverlay != null) {
            return; // Already created
        }
        
        Log.d(TAG, "📱 CREATING BLOCKING OVERLAY: Full screen coverage");
        
        // Create overlay view
        blockingOverlay = new FrameLayout(this);
        blockingOverlay.setBackgroundColor(getResources().getColor(R.color.black_overlay, getTheme()));
        blockingOverlay.setClickable(true);
        blockingOverlay.setFocusable(true);
        
        // Add overlay to activity
        ViewGroup rootView = findViewById(android.R.id.content);
        if (rootView != null) {
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            );
            rootView.addView(blockingOverlay, params);
        }
        
        Log.d(TAG, "📱 BLOCKING OVERLAY: Created successfully");
    }
    
    /**
     * ✅ Remove Blocking Overlay
     * Removes the full-screen overlay
     */
    private void removeBlockingOverlay() {
        if (blockingOverlay == null) {
            return; // Nothing to remove
        }
        
        Log.d(TAG, "📱 REMOVING BLOCKING OVERLAY");
        
        ViewGroup rootView = findViewById(android.R.id.content);
        if (rootView != null) {
            rootView.removeView(blockingOverlay);
        }
        
        blockingOverlay = null;
        
        Log.d(TAG, "📱 BLOCKING OVERLAY: Removed successfully");
    }
    
    /**
     * ✅ Disable User Interaction
     * Blocks all touch events when activity is blocked
     */
    private void disableUserInteraction() {
        Log.d(TAG, "🚫 DISABLING USER INTERACTION");
        
        getWindow().setFlags(
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
        );
    }
    
    /**
     * ✅ Enable User Interaction
     * Restores touch events when activity is unblocked
     */
    private void enableUserInteraction() {
        Log.d(TAG, "✅ ENABLING USER INTERACTION");
        
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
    }
    
    /**
     * ✅ Override Touch Events
     * Block all touch events when activity is blocked
     */
    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (isBlocked) {
            Log.d(TAG, "🚫 TOUCH BLOCKED: Activity is blocked by update requirement");
            return true; // Consume event, don't pass to views
        }
        return super.dispatchTouchEvent(ev);
    }
    
    /**
     * ✅ Override Key Events
     * Handle back button and other keys when blocked
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (isBlocked) {
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                Log.d(TAG, "🔙 BACK BUTTON BLOCKED: Showing exit option");
                // Let GlobalUpdateManager handle this
                return true;
            }
            // Block all other keys
            Log.d(TAG, "🚫 KEY BLOCKED: Key " + keyCode + " blocked by update requirement");
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
    
    /**
     * ✅ Check if Activity is Currently Blocked
     */
    public boolean isActivityBlocked() {
        return isBlocked;
    }
    
    /**
     * ✅ Force Update Check
     * Can be called by activities to manually trigger update check
     */
    protected void forceUpdateCheck() {
        Log.d(TAG, "🔄 FORCE UPDATE CHECK: Manually triggered");
        GlobalUpdateManager.getInstance().checkForUpdateGlobally();
    }
}

package com.ks.app.service.statussaver.ui.base;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.appcompat.app.AppCompatActivity;

import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.utils.GlobalUpdateManager;

/**
 * ✅ Base Activity - Automatic Update Detection
 * ALL activities in the app must extend this class
 * Automatically detects updates using existing InAppUpdateHelper
 * No extra code needed in individual screens
 */
public abstract class BaseActivity extends AppCompatActivity {

    private static final String TAG = "BaseActivity";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Log.d(TAG, "🚀 ACTIVITY CREATED: " + this.getClass().getSimpleName());
        Log.d(TAG, "🔄 AUTOMATIC UPDATE: Will be handled by GlobalUpdateManager");
    }
    
    @Override
    protected void onResume() {
        super.onResume();

        Log.d(TAG, "▶️ ACTIVITY RESUMED: " + this.getClass().getSimpleName());

        // Set current activity in GlobalUpdateManager
        // This automatically triggers update detection using existing InAppUpdateHelper
        GlobalUpdateManager.getInstance().setCurrentActivity(this);

        // Resume any interrupted updates using existing logic
        GlobalUpdateManager.getInstance().resumeUpdateIfInProgress();
    }
    
    @Override
    protected void onPause() {
        super.onPause();

        Log.d(TAG, "⏸️ ACTIVITY PAUSED: " + this.getClass().getSimpleName());

        // Clear current activity reference
        GlobalUpdateManager.getInstance().clearCurrentActivity();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        Log.d(TAG, "💀 ACTIVITY DESTROYED: " + this.getClass().getSimpleName());
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // Handle update results using existing InAppUpdateHelper logic
        boolean handled = GlobalUpdateManager.getInstance().handleActivityResult(requestCode, resultCode);
        if (handled) {
            Log.d(TAG, "📋 UPDATE RESULT: Handled by existing InAppUpdateHelper");
        }
    }
}

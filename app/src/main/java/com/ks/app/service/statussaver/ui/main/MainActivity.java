package com.ks.app.service.statussaver.ui.main;

import android.os.Build;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.utils.PermissionUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.content.pm.PackageManager;
import android.content.Intent;
import android.content.IntentSender;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import androidx.lifecycle.ViewModelProvider;
import com.ks.app.service.statussaver.utils.SAFUtils;

import com.ks.app.service.statussaver.utils.MediaStoreUtils;
import com.ks.app.service.statussaver.utils.InAppUpdateHelper;
import com.ks.app.service.statussaver.utils.Android15Compatibility;
import com.ks.app.service.statussaver.utils.Android15BehaviorHandler;


import com.ks.app.service.statussaver.ui.main.MainViewModel;
import com.ks.app.service.statussaver.ui.main.RuntimePermissionBottomSheetFragment;
import com.ks.app.service.statussaver.ui.main.SafPermissionActivity;

import android.widget.Toast;
import android.util.Log;
import android.view.View;

import androidx.appcompat.widget.Toolbar;
import android.view.Menu;
import android.view.MenuItem;




public class MainActivity extends AppCompatActivity
    implements RuntimePermissionBottomSheetFragment.RuntimePermissionListener {

    private static final String TAG = "MainActivity";
    private static final int REQUEST_CODE_SAF_PERMISSION = 1001;
    private MainViewModel mainViewModel;
    private ViewPager2 viewPager;
    private boolean userWentToSettings = false;

    // In-app update helper
    private InAppUpdateHelper updateHelper;

    // ✅ Flag to prevent Images/Videos tabs from refreshing during media permission changes
    private boolean isHandlingMediaPermissionChange = false;
    private boolean permissionRequestFromSavedTab = false;
    private boolean isPermissionFlowCompleted = false; // Prevent repeated permission flows and flickering
    private boolean isInitialSetupDone = false; // Track if initial setup is complete





    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // ✅ Initialize In-App Update Helper for detecting dismissed updates
        updateHelper = new InAppUpdateHelper(this);

        // Set status bar color to match header background for consistency
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setStatusBarColor(getResources().getColor(R.color.header_background, getTheme()));
        }

        Log.d(TAG, "🚀 MainActivity onCreate - App started successfully after splash");

        // ✅ Initialize Android 15 compatibility and behavior changes
        Android15Compatibility.initialize(this);
        Android15BehaviorHandler.initialize(this);
        Log.d(TAG, "📱 Android Compatibility: " + Android15Compatibility.getCompatibilityStatus());
        Log.d(TAG, "🎯 Android 15 Compliance: " + Android15BehaviorHandler.getComplianceStatus());

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        // ✅ Ensure 3-dot menu icon is always white and visible
        setupToolbarMenuIconColor(toolbar);

        mainViewModel = new ViewModelProvider(this).get(MainViewModel.class);
        viewPager = findViewById(R.id.view_pager);



        // ✅ SINGLE PERMISSION FLOW - Only call once in onCreate to prevent loops
        if (!isPermissionFlowCompleted) {
            Log.d(TAG, "🔄 Starting initial permission flow (only once)");
            handlePermissionsAndInitialLoad();
        } else {
            Log.d(TAG, "✅ Permission flow already completed - skipping");
        }

        // ✅ DISABLE ViewModel observer that causes permission loops
        // This observer was triggering repeated permission checks
        // mainViewModel.getIsSafAccessPromptNeeded().observe(this, needsPrompt -> {
        //     if (needsPrompt != null && needsPrompt && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        //         Log.d(TAG, "isSafAccessPromptNeeded LiveData triggered. Re-checking access.");
        //         checkAndShowPermissionPrompts();
        //         mainViewModel.resetSafAccessPromptNeeded();
        //     }
        // });
    }

    /**
     * ✅ COMPLETELY REWRITTEN HOME SCREEN FLOW
     *
     * Proper sequence:
     * 1. Load UI immediately (no waiting)
     * 2. Handle gallery permissions (no automatic popups)
     * 3. Let user trigger permission requests when needed
     */
    private void handlePermissionsAndInitialLoad() {
        try {
            Log.d(TAG, "🏠 Starting home screen flow");

            // ✅ Prevent repeated calls that cause flickering
            if (isPermissionFlowCompleted || isInitialSetupDone) {
                Log.d(TAG, "✅ Home screen flow already completed - preventing duplicates");
                return;
            }

            // Mark as started to prevent repeated calls
            isInitialSetupDone = true;

            // ✅ STEP 1: IMMEDIATE UI SETUP - No waiting for anything
            Log.d(TAG, "🎯 Step 1: Setting up UI immediately");
            setupViewPagerAndTabs();
            if (mainViewModel != null) {
                // ✅ Images/Videos tabs load immediately (use folder access, not media permissions)
                Log.d(TAG, "Loading Images/Videos tabs immediately (folder access)");
                mainViewModel.loadWhatsAppStatuses();

                // ✅ Saved tab loads based on media permissions (may be empty if no permissions)
                Log.d(TAG, "Loading Saved tab (depends on media permissions)");
                mainViewModel.loadSavedMedia();
            }

            // ✅ STEP 2: CHECK GALLERY PERMISSIONS
            Log.d(TAG, "🔄 Step 2: Checking gallery permissions");
            checkGalleryPermissions();
        } catch (Exception e) {
            Log.e(TAG, "Error in handlePermissionsAndInitialLoad: " + e.getMessage(), e);
            // Even if permissions fail, ensure UI is set up and data loads
            try {
                setupViewPagerAndTabs();
                if (mainViewModel != null) {
                    mainViewModel.loadWhatsAppStatuses();
                    mainViewModel.loadSavedMedia();
                }
            } catch (Exception fallbackError) {
                Log.e(TAG, "Fallback UI setup also failed: " + fallbackError.getMessage(), fallbackError);
            }

            // Old method call removed - new flow handles this properly
        }
    }



    /**
     * ✅ STEP 3: GALLERY PERMISSION CHECK
     *
     * Check permissions silently.
     * User will be prompted only when they access Saved tab.
     */
    private void checkGalleryPermissions() {
        try {
            Log.d(TAG, "🔍 Step 3: Checking gallery permissions");

            // Add a small delay to ensure UI is fully loaded
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    boolean hasPermissions = PermissionUtils.hasPermissions(this);
                    Log.d(TAG, "Gallery permissions status: " + (hasPermissions ? "✅ GRANTED" : "❌ NOT GRANTED"));

                    if (hasPermissions) {
                        Log.d(TAG, "✅ Gallery permissions already granted - saved tab will work");
                    } else {
                        Log.d(TAG, "❌ Gallery permissions not granted - user will be prompted when accessing Saved tab");
                    }

                    // ✅ NO AUTOMATIC POPUPS - let user trigger permission requests when needed
                    // Mark flow as completed
                    isPermissionFlowCompleted = true;
                    Log.d(TAG, "🏁 Home screen flow completed: UI → Permissions");

                } catch (Exception e) {
                    Log.e(TAG, "Error in delayed permission check: " + e.getMessage(), e);
                }
            }, 500); // 0.5 second delay after UI load

        } catch (Exception e) {
            Log.e(TAG, "Error in checkGalleryPermissions: " + e.getMessage(), e);
        }
    }

    /**
     * ✅ DISABLED OLD PERMISSION METHOD
     *
     * This method was causing permission loops and flickering.
     * Replaced with new silent permission checking.
     */
    private void ensureRuntimePermissionsAndLoad() {
        Log.d(TAG, "❌ OLD METHOD: ensureRuntimePermissionsAndLoad - this method is disabled to prevent loops");
        // This method is intentionally disabled to prevent permission loops
        return;

        // All old permission logic removed to prevent loops
    }

    /**
     * ✅ DISABLED OLD BACKGROUND PERMISSION METHOD
     */
    private void ensureRuntimePermissionsInBackground() {
        Log.d(TAG, "❌ OLD METHOD: ensureRuntimePermissionsInBackground - disabled to prevent loops");
        return;
        // All old background permission logic removed
    }

    /**
     * ✅ REMOVED OLD PERMISSION CHECKING METHOD
     *
     * This method was causing loops and flickering.
     * Replaced with new silent permission checking.
     */
    private void checkAndShowPermissionPrompts() {
        Log.d(TAG, "❌ OLD METHOD: checkAndShowPermissionPrompts - this method is disabled to prevent loops");
        // This method is intentionally disabled to prevent permission loops
        return;
    }

    private void setupViewPagerAndTabs() {
        try {
            if (viewPager == null) {
                Log.e(TAG, "setupViewPagerAndTabs: viewPager is null");
                return;
            }

            Log.d(TAG, "setupViewPagerAndTabs: Setting up adapter and tabs.");
            TabLayout tabLayout = findViewById(R.id.tab_layout);

            if (tabLayout == null) {
                Log.e(TAG, "setupViewPagerAndTabs: tabLayout is null");
                return;
            }

            try {
                // Use the main pager adapter (fallback to existing working fragments)
                MainPagerAdapter adapter = new MainPagerAdapter(this);
                viewPager.setAdapter(adapter);

                // Configure ViewPager2 for better performance
                viewPager.setOffscreenPageLimit(3); // Keep all tabs in memory
                viewPager.setUserInputEnabled(true); // Allow swiping

                new TabLayoutMediator(tabLayout, viewPager,
                        (tab, position) -> {
                            try {
                                // Show tabs with icons and text
                                switch (position) {
                                    case 0:
                                        tab.setText(getString(R.string.images_tab_title));
                                        tab.setIcon(R.drawable.ic_tab_images);
                                        break;
                                    case 1:
                                        tab.setText(getString(R.string.videos_tab_title));
                                        tab.setIcon(R.drawable.ic_tab_videos);
                                        break;
                                    case 2:
                                        tab.setText(getString(R.string.saved_tab_title));
                                        tab.setIcon(R.drawable.ic_tab_saved);
                                        break;
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error setting tab text and icon: " + e.getMessage(), e);
                                // Fallback tab names with icons
                                switch (position) {
                                    case 0:
                                        tab.setText("Images");
                                        tab.setIcon(R.drawable.ic_tab_images);
                                        break;
                                    case 1:
                                        tab.setText("Videos");
                                        tab.setIcon(R.drawable.ic_tab_videos);
                                        break;
                                    case 2:
                                        tab.setText("Saved");
                                        tab.setIcon(R.drawable.ic_tab_saved);
                                        break;
                                }
                            }
                        }
                ).attach();

                Log.d(TAG, "ViewPager and tabs setup complete");
            } catch (Exception e) {
                Log.e(TAG, "Error setting up ViewPager adapter: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in setupViewPagerAndTabs: " + e.getMessage(), e);
        }
    }

    /**
     * ✅ Refresh only Saved tab data without affecting Images/Videos tabs
     * This prevents unnecessary reloading of Images/Videos when permissions change
     */
    private void refreshSavedTabOnly() {
        try {
            Log.d(TAG, "refreshSavedTabOnly: Refreshing only Saved tab data");

            if (mainViewModel != null) {
                Log.d(TAG, "Reloading Saved tab data only");
                mainViewModel.loadSavedMedia();
            } else {
                Log.w(TAG, "mainViewModel is null, cannot refresh Saved tab");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error refreshing Saved tab: " + e.getMessage(), e);
        }
    }

    private void refreshTabsForPermissionChange() {
        try {
            Log.d(TAG, "refreshTabsForPermissionChange: Refreshing tabs due to permission change");

            if (viewPager == null) {
                Log.e(TAG, "refreshTabsForPermissionChange: viewPager is null");
                return;
            }

            TabLayout tabLayout = findViewById(R.id.tab_layout);
            if (tabLayout == null) {
                Log.e(TAG, "refreshTabsForPermissionChange: tabLayout is null");
                return;
            }

            // Clear existing adapter
            viewPager.setAdapter(null);

            // Create new adapter with current permission state
            MainPagerAdapter adapter = new MainPagerAdapter(this);
            viewPager.setAdapter(adapter);

            // Configure ViewPager2 for better performance
            viewPager.setOffscreenPageLimit(3);
            viewPager.setUserInputEnabled(true);

            // Setup tabs with new adapter
            new TabLayoutMediator(tabLayout, viewPager,
                    (tab, position) -> {
                        try {
                            // Show tabs with icons and text
                            switch (position) {
                                case 0:
                                    tab.setText(getString(R.string.images_tab_title));
                                    tab.setIcon(R.drawable.ic_tab_images);
                                    break;
                                case 1:
                                    tab.setText(getString(R.string.videos_tab_title));
                                    tab.setIcon(R.drawable.ic_tab_videos);
                                    break;
                                case 2:
                                    tab.setText(getString(R.string.saved_tab_title));
                                    tab.setIcon(R.drawable.ic_tab_saved);
                                    break;
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error setting tab text and icon in refresh: " + e.getMessage(), e);
                            // Fallback tab names with icons
                            switch (position) {
                                case 0:
                                    tab.setText("Images");
                                    tab.setIcon(R.drawable.ic_tab_images);
                                    break;
                                case 1:
                                    tab.setText("Videos");
                                    tab.setIcon(R.drawable.ic_tab_videos);
                                    break;
                                case 2:
                                    tab.setText("Saved");
                                    tab.setIcon(R.drawable.ic_tab_saved);
                                    break;
                            }
                        }
                    }
            ).attach();

            Log.d(TAG, "Tabs refreshed successfully. Always showing 3 tabs.");

        } catch (Exception e) {
            Log.e(TAG, "Error in refreshTabsForPermissionChange: " + e.getMessage(), e);
        }
    }



    @Override
    public void onGoToSettings() {
        try {
            // Set flag to track that user went to settings
            userWentToSettings = true;
            Log.d(TAG, "User going to settings - setting flag");

            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            String packageName = getPackageName();
            if (packageName != null) {
                Uri uriValue = Uri.fromParts("package", packageName, null);
                intent.setData(uriValue);
                startActivity(intent);
            } else {
                Log.e(TAG, "Package name is null, cannot open settings");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error opening settings: " + e.getMessage(), e);
        }
    }

    @Override
    public void onCancelRuntimePermission() {
        try {
            Log.d(TAG, "User cancelled runtime permission request");

            // ✅ Check if permission request came from Saved tab
            if (permissionRequestFromSavedTab) {
                Log.d(TAG, "Permission cancelled from Saved tab - staying on current tab");
                permissionRequestFromSavedTab = false; // Reset flag
                // Don't switch tabs - user stays on Saved tab with empty state
                return;
            }

            // Runtime permissions denied - app may not function correctly
        } catch (Exception e) {
            Log.e(TAG, "Error in onCancelRuntimePermission: " + e.getMessage(), e);
        }
    }

    /**
     * Public method to request runtime permissions - can be called from fragments
     * ✅ Modified to NOT reload Images/Videos tabs when called from fragments
     */
    public void requestRuntimePermissions() {
        try {
            Log.d(TAG, "requestRuntimePermissions called from fragment - requesting permissions only (no tab refresh)");

            // ✅ Only request permissions, don't reload Images/Videos tabs
            if (this != null && !isFinishing() && !isDestroyed()) {
                boolean hasPermissions = PermissionUtils.hasPermissions(this);
                if (!hasPermissions) {
                    boolean isRequestInProgress = PermissionUtils.isPermissionRequestInProgress(this);
                    if (!isRequestInProgress) {
                        PermissionUtils.requestPermissions(this);
                    }
                } else {
                    Log.d(TAG, "Permissions already granted - only refreshing Saved tab");
                    if (mainViewModel != null) {
                        isHandlingMediaPermissionChange = true;
                        mainViewModel.setSkipImagesVideosRefresh(true);
                        mainViewModel.loadSavedMediaOnly();
                        mainViewModel.setSkipImagesVideosRefresh(false);
                        isHandlingMediaPermissionChange = false;
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in requestRuntimePermissions: " + e.getMessage(), e);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        try {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);

            // ✅ Null safety for permission result handling
            try {
                if (this != null && !isFinishing() && !isDestroyed()) {
                    PermissionUtils.onRequestPermissionsResultReceived();
                } else {
                    Log.w(TAG, "Activity is null, finishing, or destroyed - skipping permission result handling");
                    return;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error in onRequestPermissionsResultReceived: " + e.getMessage(), e);
            }

            if (requestCode == PermissionUtils.PERMISSIONS_REQUEST_CODE) {
                boolean hasPermissions = false;
                try {
                    // ✅ Null safety for permission check in result
                    if (this != null && !isFinishing() && !isDestroyed()) {
                        hasPermissions = PermissionUtils.hasPermissions(this);
                    } else {
                        Log.w(TAG, "Activity is null, finishing, or destroyed - skipping permission check in result");
                        hasPermissions = false;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error checking permissions in result: " + e.getMessage(), e);
                    hasPermissions = false;
                } catch (Throwable t) {
                    Log.e(TAG, "Unexpected error checking permissions in result: " + t.getMessage(), t);
                    hasPermissions = false;
                }

                if (hasPermissions) {
                    Log.d(TAG, "Runtime permissions GRANTED after request.");

                    // Reset the Saved tab flag since permission was granted
                    permissionRequestFromSavedTab = false;
                    isPermissionFlowCompleted = true; // Mark permission flow as completed

                    try {
                        // Remember current tab position before setting up ViewPager
                        int currentTab = 0;
                        if (viewPager != null && viewPager.getAdapter() != null) {
                            currentTab = viewPager.getCurrentItem();
                            Log.d(TAG, "Remembering current tab position: " + currentTab);
                        }

                        if (viewPager != null && viewPager.getAdapter() == null) {
                            setupViewPagerAndTabs();

                            // Restore the current tab position (don't switch to Images tab)
                            if (currentTab > 0) {
                                Log.d(TAG, "Restoring tab position to: " + currentTab + " (preventing switch to Images)");
                                viewPager.setCurrentItem(currentTab, false);
                            }
                        }

                        // ✅ Use new method to load ONLY Saved tab
                        if (mainViewModel != null) {
                            Log.d(TAG, "Media permissions granted - loading ONLY Saved tab (Images/Videos unchanged)");
                            isHandlingMediaPermissionChange = true;
                            mainViewModel.setSkipImagesVideosRefresh(true);
                            mainViewModel.loadSavedMediaOnly();
                            mainViewModel.setSkipImagesVideosRefresh(false);
                            isHandlingMediaPermissionChange = false;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error setting up UI after permission grant: " + e.getMessage(), e);
                    }
                } else {
                    Log.d(TAG, "Runtime permissions DENIED after request.");

                    // ✅ DOUBLE-CHECK permissions before showing denial UI
                    // Sometimes the permission check fails immediately after grant
                    boolean recheckPermissions = false;
                    try {
                        // Wait a moment and recheck permissions
                        Thread.sleep(100);
                        if (this != null && !isFinishing() && !isDestroyed()) {
                            recheckPermissions = PermissionUtils.hasPermissions(this);
                            Log.d(TAG, "Rechecked permissions after delay: " + recheckPermissions);
                        }
                    } catch (Exception e) {
                        Log.w(TAG, "Error rechecking permissions: " + e.getMessage());
                    }

                    if (recheckPermissions) {
                        Log.d(TAG, "✅ Permissions actually granted on recheck - setting up UI");
                        // Permissions are actually granted, set up UI
                        try {
                            setupViewPagerAndTabs();
                            if (mainViewModel != null) {
                                mainViewModel.loadSavedMedia();
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error setting up UI after recheck: " + e.getMessage());
                        }
                        return;
                    }

                    // Check if permission request came from Saved tab
                    if (permissionRequestFromSavedTab) {
                        Log.d(TAG, "Permission denied from Saved tab - showing gallery permission bottom sheet, staying on current tab");
                        permissionRequestFromSavedTab = false; // Reset flag
                        showGalleryPermissionBottomSheet();
                        return; // Don't switch tabs - stay on current tab
                    }

                    boolean shouldShowSettingsLink = false;

                    try {
                        if (permissions != null) {
                            for(String permission : permissions) {
                                if (permission != null) {
                                    boolean shouldShow = ActivityCompat.shouldShowRequestPermissionRationale(this, permission);
                                    int permissionStatus = ContextCompat.checkSelfPermission(this, permission);

                                    if (!shouldShow && permissionStatus != PackageManager.PERMISSION_GRANTED) {
                                        shouldShowSettingsLink = true;
                                        break;
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking permission rationale: " + e.getMessage(), e);
                        shouldShowSettingsLink = true; // Default to showing settings link on error
                    }

                    if (shouldShowSettingsLink) {
                        Log.i(TAG, "Runtime permission denied with Don't Ask Again. Showing settings bottom sheet.");
                        try {
                            showRuntimePermissionDeniedBottomSheet();
                        } catch (Exception e) {
                            Log.e(TAG, "Error showing permission denied bottom sheet: " + e.getMessage(), e);
                        }
                    } else {
                        Log.i(TAG, "Runtime permission denied, can ask again or partially granted. User will be prompted later or can refresh.");
                        try {
                            // Remember current tab position before setting up ViewPager
                            int currentTab = 0;
                            if (viewPager != null && viewPager.getAdapter() != null) {
                                currentTab = viewPager.getCurrentItem();
                                Log.d(TAG, "Remembering current tab position: " + currentTab);
                            }

                            if (viewPager != null && viewPager.getAdapter() == null) {
                                setupViewPagerAndTabs();

                                // Restore the current tab position (don't switch to Images tab)
                                if (currentTab > 0) {
                                    Log.d(TAG, "Restoring tab position to: " + currentTab + " (preventing switch to Images)");
                                    viewPager.setCurrentItem(currentTab, false);
                                }
                            }

                            // ✅ Ensure Images/Videos tabs are protected from any refresh attempts
                            if (mainViewModel != null) {
                                Log.d(TAG, "Media permissions denied - ensuring NO tab refresh (Images/Videos unchanged, Saved shows empty state)");
                                isHandlingMediaPermissionChange = true;
                                mainViewModel.setSkipImagesVideosRefresh(true);
                                // Don't call loadSavedMedia() - let it show empty state naturally
                                mainViewModel.setSkipImagesVideosRefresh(false);
                                isHandlingMediaPermissionChange = false;
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error handling partial permission grant: " + e.getMessage(), e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onRequestPermissionsResult: " + e.getMessage(), e);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        try {
            super.onActivityResult(requestCode, resultCode, data);

            // ✅ Handle in-app update results first
            if (updateHelper != null && updateHelper.handleActivityResult(requestCode, resultCode)) {
                Log.d(TAG, "Update result handled by InAppUpdateHelper");
                return;
            }

            if (requestCode == REQUEST_CODE_SAF_PERMISSION) {
                if (resultCode == RESULT_OK) {
                    Log.d(TAG, "SAF permission activity returned success");
                    // Permission was granted, refresh the UI
                    ensureRuntimePermissionsAndLoad();
                } else {
                    Log.d(TAG, "SAF permission activity was cancelled or failed");
                    // User cancelled or permission was denied
                    // Could show a message or handle accordingly
                }
            } else if (requestCode == SAFUtils.REQUEST_CODE_SAF_WHATSAPP_STATUSES) {
                if (resultCode == RESULT_OK && data != null) {
                    // ✅ Null safety for SAF result handling
                    try {
                        if (this != null && !isFinishing() && !isDestroyed()) {
                            // SAF permission granted - persist the URI
                            String treeUriKey = SAFUtils.getWhatsAppStatusesTreeUriKey();
                            if (treeUriKey != null) {
                                SAFUtils.persistFolderUri(this, data, treeUriKey);
                            } else {
                                Log.w(TAG, "Tree URI key is null - cannot persist SAF URI");
                            }

                            ensureRuntimePermissionsAndLoad();
                        } else {
                            Log.w(TAG, "Activity is null, finishing, or destroyed - skipping SAF result handling");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error handling SAF success result: " + e.getMessage(), e);
                    }
                } else {
                    // SAF permission denied
                    try {

                        // Show SAF screen again for Android 10+
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            try {
                                boolean hasRuntimeDialog = getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG) != null;

                                if (!hasRuntimeDialog) {
                                    Intent safIntent = new Intent(this, SafPermissionActivity.class);
                                    startActivityForResult(safIntent, REQUEST_CODE_SAF_PERMISSION);
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error launching SAF permission activity after cancellation: " + e.getMessage(), e);
                            }
                        } else {
                            Log.i(TAG, "WhatsApp .Statuses folder not found. Not re-prompting SAF.");
                            ensureRuntimePermissionsAndLoad();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error handling SAF cancellation: " + e.getMessage(), e);
                    }
                }
            }


        } catch (Exception e) {
            Log.e(TAG, "Error in onActivityResult: " + e.getMessage(), e);
        }
    }

    private void showRuntimePermissionDeniedBottomSheet() {
        try {
            // ✅ Null safety for showing permission denied bottom sheet
            if (this == null || isFinishing() || isDestroyed()) {
                Log.w(TAG, "Activity is null, finishing, or destroyed - cannot show permission denied bottom sheet");
                return;
            }

            boolean hasRuntimeDialog = false;

            try {
                if (getSupportFragmentManager() != null) {
                    hasRuntimeDialog = getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG) != null;
                } else {
                    Log.w(TAG, "Fragment manager is null - cannot check existing dialogs");
                    return;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error checking existing dialogs in showRuntimePermissionDeniedBottomSheet: " + e.getMessage(), e);
                return;
            }

            if (!hasRuntimeDialog) {
                try {
                    RuntimePermissionBottomSheetFragment bottomSheet = new RuntimePermissionBottomSheetFragment();
                    if (bottomSheet != null) {
                        bottomSheet.setCancelable(false);
                        bottomSheet.show(getSupportFragmentManager(), RuntimePermissionBottomSheetFragment.TAG);
                    } else {
                        Log.e(TAG, "Failed to create RuntimePermissionBottomSheetFragment");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error showing runtime permission denied bottom sheet: " + e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in showRuntimePermissionDeniedBottomSheet: " + e.getMessage(), e);
        } catch (Throwable t) {
            Log.e(TAG, "Unexpected error in showRuntimePermissionDeniedBottomSheet: " + t.getMessage(), t);
        }
    }

    @Override
    protected void onResume() {
        try {
            super.onResume();
            Log.d(TAG, "onResume: Re-evaluating permissions and loading state.");

            // ✅ CRITICAL: Check for dismissed update UI (X, back, task swipe)
            if (updateHelper != null) {
                updateHelper.resumeUpdateIfInProgress();
            }

            try {
                // ✅ Check if permissions are now granted with null safety
                boolean hasRuntimePermissions = false;
                boolean hasSafPermissions = true;

                // Check runtime permissions with null safety
                try {
                    if (this != null && !isFinishing() && !isDestroyed()) {
                        hasRuntimePermissions = PermissionUtils.hasPermissions(this);
                    } else {
                        Log.w(TAG, "Activity is null, finishing, or destroyed - skipping runtime permission check in onResume");
                        hasRuntimePermissions = false;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error checking runtime permissions in onResume: " + e.getMessage(), e);
                    hasRuntimePermissions = false;
                } catch (Throwable t) {
                    Log.e(TAG, "Unexpected error checking runtime permissions in onResume: " + t.getMessage(), t);
                    hasRuntimePermissions = false;
                }

                // Check SAF permissions with null safety
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    try {
                        if (this != null && !isFinishing() && !isDestroyed()) {
                            hasSafPermissions = SAFUtils.hasWhatsAppStatusAccess(this);
                        } else {
                            Log.w(TAG, "Activity is null, finishing, or destroyed - skipping SAF permission check in onResume");
                            hasSafPermissions = true; // Don't block if activity is invalid
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking SAF permissions in onResume: " + e.getMessage(), e);
                        hasSafPermissions = true; // Don't block if SAF check fails
                    } catch (Throwable t) {
                        Log.e(TAG, "Unexpected error checking SAF permissions in onResume: " + t.getMessage(), t);
                        hasSafPermissions = true; // Don't block if SAF check fails
                    }
                }

                Log.d(TAG, "onResume - Runtime permissions: " + hasRuntimePermissions + ", SAF permissions: " + hasSafPermissions + ", userWentToSettings: " + userWentToSettings);

                // If permissions are now granted, dismiss any permission dialogs and setup UI
                if (hasRuntimePermissions && hasSafPermissions) {
                    Log.d(TAG, "All permissions granted in onResume - setting up UI");

                    // ✅ Dismiss any existing permission bottom sheets with null safety
                    try {
                        if (getSupportFragmentManager() != null && this != null && !isFinishing() && !isDestroyed()) {
                            boolean hasRuntimeDialog = getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG) != null;

                            if (hasRuntimeDialog) {
                                Log.d(TAG, "Dismissing permission dialogs as permissions are now granted");

                                if (hasRuntimeDialog) {
                                    try {
                                        RuntimePermissionBottomSheetFragment fragment = (RuntimePermissionBottomSheetFragment)
                                            getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG);
                                        if (fragment != null && fragment.isAdded() && !fragment.isDetached()) {
                                            fragment.dismiss();
                                        }
                                    } catch (Exception e) {
                                        Log.e(TAG, "Error dismissing runtime permission dialog: " + e.getMessage(), e);
                                    }
                                }
                            }
                        } else {
                            Log.w(TAG, "Fragment manager is null or activity is invalid - cannot dismiss permission dialogs");
                        }

                        // Reset flag if user came back from settings
                        if (userWentToSettings) {
                            userWentToSettings = false; // Reset flag
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking/dismissing permission dialogs: " + e.getMessage(), e);
                    }

                    // Setup UI if not already done
                    try {
                        // Remember current tab position before setting up ViewPager
                        int currentTab = 0;
                        if (viewPager != null && viewPager.getAdapter() != null) {
                            currentTab = viewPager.getCurrentItem();
                            Log.d(TAG, "Remembering current tab position in onResume: " + currentTab);
                        }

                        if (viewPager != null && viewPager.getAdapter() == null) {
                            setupViewPagerAndTabs();

                            // Restore the current tab position (don't switch to Images tab)
                            if (currentTab > 0) {
                                Log.d(TAG, "Restoring tab position in onResume to: " + currentTab + " (preventing switch to Images)");
                                viewPager.setCurrentItem(currentTab, false);
                            }
                        }

                        // ✅ Use protected method when returning from settings
                        if (mainViewModel != null) {
                            Log.d(TAG, "Returned from settings - loading ONLY Saved tab (Images/Videos unchanged)");
                            isHandlingMediaPermissionChange = true;
                            mainViewModel.setSkipImagesVideosRefresh(true);
                            mainViewModel.loadSavedMediaOnly();
                            mainViewModel.setSkipImagesVideosRefresh(false);
                            isHandlingMediaPermissionChange = false;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error setting up UI after permission grant: " + e.getMessage(), e);
                    }
                } else {
                    // ✅ PREVENT PERMISSION LOOPS - Never call permission flow from onResume
                    Log.d(TAG, "Some permissions still missing in onResume - but NOT calling permission flow to prevent loops");
                    Log.d(TAG, "User can manually grant permissions or restart app if needed");
                    // DO NOT call any permission methods here - this causes infinite loops
                }

            } catch (Exception e) {
                Log.e(TAG, "Error handling permissions in onResume: " + e.getMessage(), e);
                // Fallback to normal permission handling
                try {
                    handlePermissionsAndInitialLoad();
                } catch (Exception fallbackError) {
                    Log.e(TAG, "Fallback permission handling also failed: " + fallbackError.getMessage(), fallbackError);
                }
            }

            // Ensure data is loaded when returning from preview (only if UI is set up)
            try {
                if (mainViewModel != null && viewPager != null && viewPager.getAdapter() != null) {
                    Log.d(TAG, "MainActivity onResume - ensuring data is loaded for preview return");
                    // Trigger a refresh to ensure all tabs have current data
                    mainViewModel.loadAllMedia();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error loading data in onResume: " + e.getMessage(), e);
            }



        } catch (Exception e) {
            Log.e(TAG, "Error in onResume: " + e.getMessage(), e);
        }
    }



    /**
     * ✅ Setup toolbar menu icon color to ensure 3-dot menu is always white and visible
     */
    private void setupToolbarMenuIconColor(Toolbar toolbar) {
        try {
            if (toolbar != null) {
                // Method 1: Try to set custom white overflow icon
                try {
                    toolbar.setOverflowIcon(getResources().getDrawable(R.drawable.ic_menu_overflow_white, getTheme()));
                    Log.d(TAG, "✅ Toolbar 3-dot menu icon set to custom white icon");
                } catch (Exception e1) {
                    // Method 2: Fallback - tint the default overflow icon
                    try {
                        if (toolbar.getOverflowIcon() != null) {
                            toolbar.getOverflowIcon().setTint(getResources().getColor(android.R.color.white, getTheme()));
                            Log.d(TAG, "✅ Toolbar 3-dot menu icon tinted white (fallback)");
                        }
                    } catch (Exception e2) {
                        Log.w(TAG, "Could not set overflow icon color, using default");
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting toolbar menu icon color: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);

        // ✅ Ensure menu icons are white after menu creation
        try {
            for (int i = 0; i < menu.size(); i++) {
                MenuItem item = menu.getItem(i);
                if (item != null && item.getIcon() != null) {
                    item.getIcon().setTint(getResources().getColor(android.R.color.white, getTheme()));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting menu item colors: " + e.getMessage(), e);
        }

        // ✅ Ensure 3-dot overflow icon is white after menu creation
        try {
            Toolbar toolbar = findViewById(R.id.toolbar);
            if (toolbar != null) {
                // Post to ensure this runs after menu inflation is complete
                toolbar.post(() -> setupToolbarMenuIconColor(toolbar));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting overflow icon after menu creation: " + e.getMessage(), e);
        }

        return true;
    }



    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        try {
            if (item == null) {
                return super.onOptionsItemSelected(item);
            }

            int id = item.getItemId();
            if (id == R.id.action_refresh) {
                // Refresh based on active tab
                try {
                    if (viewPager != null && mainViewModel != null) {
                        int position = viewPager.getCurrentItem();
                        MainPagerAdapter adapter = (MainPagerAdapter) viewPager.getAdapter();
                        // Always handle all 3 tabs
                        switch (position) {
                            case 0: // Images
                                mainViewModel.loadWhatsAppStatuses();
                                break;
                            case 1: // Videos
                                mainViewModel.loadWhatsAppStatuses();
                                break;
                            case 2: // Saved
                                mainViewModel.loadSavedMedia();
                                break;
                        }
                    } else {
                        Log.w(TAG, "ViewPager or MainViewModel is null, cannot refresh");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error refreshing data: " + e.getMessage(), e);
                }
                return true;
            } else if (id == R.id.action_settings) {
                try {
                    // Open settings activity (to be created)
                    Intent intent = new Intent(this, SettingsActivity.class);
                    startActivity(intent);
                } catch (Exception e) {
                    Log.e(TAG, "Error opening settings: " + e.getMessage(), e);
                }
                return true;
            }
            return super.onOptionsItemSelected(item);
        } catch (Exception e) {
            Log.e(TAG, "Error in onOptionsItemSelected: " + e.getMessage(), e);
            return super.onOptionsItemSelected(item);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
    }



    @Override
    public void onLowMemory() {
        super.onLowMemory();
        Log.w(TAG, "Low memory warning - clearing Glide cache");

        // Clear Glide memory when system is low on memory
        com.bumptech.glide.Glide.get(this).clearMemory();

        // Clear disk cache in background
        new Thread(() -> com.bumptech.glide.Glide.get(this).clearDiskCache()).start();
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        Log.w(TAG, "Memory trim requested - level: " + level);

        // Clear Glide memory on memory pressure
        if (level >= TRIM_MEMORY_MODERATE) {
            com.bumptech.glide.Glide.get(this).clearMemory();
        }
    }





    /**
     * ✅ Set flag to track if permission request came from Saved tab
     */
    public void setPermissionRequestFromSavedTab(boolean fromSavedTab) {
        this.permissionRequestFromSavedTab = fromSavedTab;
        Log.d(TAG, "🏷️ Permission request from Saved tab flag set to: " + fromSavedTab);
        if (fromSavedTab) {
            Log.d(TAG, "🎯 SAVED TAB: Permission request originated from Saved tab - will prevent tab switching on denial");
        }
    }



    /**
     * ✅ Show Gallery Permission Bottom Sheet
     * Shows existing gallery permission bottom sheet when user denies from Saved tab
     */
    private void showGalleryPermissionBottomSheet() {
        Log.d(TAG, "Showing existing gallery permission bottom sheet for Saved tab denial");

        try {
            // Show the existing RuntimePermissionBottomSheetFragment
            if (getSupportFragmentManager() != null) {
                // Check if dialog is already showing
                if (getSupportFragmentManager().findFragmentByTag("RuntimePermissionBottomSheetFragment") == null) {
                    RuntimePermissionBottomSheetFragment bottomSheet = new RuntimePermissionBottomSheetFragment();
                    bottomSheet.setCancelable(true);
                    bottomSheet.show(getSupportFragmentManager(), "RuntimePermissionBottomSheetFragment");
                    Log.d(TAG, "Gallery permission bottom sheet shown for Saved tab denial");
                } else {
                    Log.d(TAG, "Gallery permission bottom sheet already showing");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing gallery permission bottom sheet: " + e.getMessage(), e);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Clear Glide memory to prevent leaks
        if (!isFinishing()) {
            com.bumptech.glide.Glide.get(this).clearMemory();
        }

        Log.d(TAG, "🗑️ MainActivity destroyed");

        // MainActivity cleanup completed
    }

    // All in-app update functionality moved to SplashActivity

}
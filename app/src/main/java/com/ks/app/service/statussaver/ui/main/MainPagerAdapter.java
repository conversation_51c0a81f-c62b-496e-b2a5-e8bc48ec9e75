package com.ks.app.service.statussaver.ui.main;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import com.ks.app.service.statussaver.ui.images.OptimizedImageTabFragment;
import com.ks.app.service.statussaver.ui.videos.OptimizedVideoTabFragment;
import com.ks.app.service.statussaver.ui.saved.OptimizedSavedTabFragment;
import com.ks.app.service.statussaver.utils.PermissionUtils;

public class MainPagerAdapter extends FragmentStateAdapter {

    private final FragmentActivity activity;
    private final boolean hasGalleryPermissions;

    public MainPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
        this.activity = fragmentActivity;
        this.hasGalleryPermissions = PermissionUtils.hasPermissions(fragmentActivity);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        // Always show all 3 tabs regardless of permissions
        switch (position) {
            case 0:
                return new OptimizedImageTabFragment();
            case 1:
                return new OptimizedVideoTabFragment();
            case 2:
                return new OptimizedSavedTabFragment();
            default:
                return new Fragment(); // Should not happen
        }
    }

    @Override
    public int getItemCount() {
        // Always return 3 tabs regardless of permissions
        return 3;
    }

    public boolean hasGalleryPermissions() {
        return hasGalleryPermissions;
    }
}
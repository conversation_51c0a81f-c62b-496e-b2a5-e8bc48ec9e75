package com.ks.app.service.statussaver.ui.main;

import android.app.Application;
import android.content.Context;
import android.os.Build;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.ks.app.service.statussaver.data.models.MediaItem;
import com.ks.app.service.statussaver.data.repository.MediaRepository;
import com.ks.app.service.statussaver.utils.MediaStoreUtils;
import com.ks.app.service.statussaver.utils.SAFUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainViewModel extends AndroidViewModel {
    private static final String TAG = "MainViewModel";

    // Repository for handling data efficiently
    private final MediaRepository repository;

    // Legacy LiveData for compatibility (can be removed later)
    private final MutableLiveData<List<MediaItem>> imageStatuses = new MutableLiveData<>();
    private final MutableLiveData<List<MediaItem>> videoStatuses = new MutableLiveData<>();
    private final MutableLiveData<List<MediaItem>> savedMedia = new MutableLiveData<>();

    private final MutableLiveData<String> toastMessage = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isSafAccessPromptNeeded = new MutableLiveData<>(false);

    private final ExecutorService executorService = Executors.newFixedThreadPool(3); // For image, video, saved
    private final Context appContext;

    // ✅ Flag to prevent Images/Videos tabs from refreshing during media permission changes
    private boolean skipImagesVideosRefresh = false;

    public MainViewModel(@NonNull Application application) {
        super(application);
        this.appContext = application.getApplicationContext();
        this.repository = new MediaRepository(appContext);
    }

    // Repository-backed methods
    public LiveData<List<MediaItem>> getImageStatuses() {
        return repository.getImageStatuses();
    }

    public LiveData<List<MediaItem>> getVideoStatuses() {
        return repository.getVideoStatuses();
    }

    public LiveData<List<MediaItem>> getSavedMedia() {
        return repository.getSavedMedia();
    }

    public LiveData<Boolean> getIsLoadingImages() {
        return repository.getIsLoadingImages();
    }

    public LiveData<Boolean> getIsLoadingVideos() {
        return repository.getIsLoadingVideos();
    }

    public LiveData<Boolean> getIsLoadingSaved() {
        return repository.getIsLoadingSaved();
    }

    public LiveData<String> getToastMessage() {
        return toastMessage;
    }

    public LiveData<Boolean> getIsSafAccessPromptNeeded() {
        return isSafAccessPromptNeeded;
    }

    public void clearToastMessage() {
        toastMessage.setValue(null); // Or an empty string, depending on how you check in observer
    }

    public LiveData<String> getErrorMessage() {
        return repository.getErrorMessage();
    }

    public void clearErrorMessage() {
        repository.clearErrorMessage();
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        repository.cleanup();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }

    public void loadAllMedia() {
        loadWhatsAppStatuses();
        loadSavedMedia();
    }

    public void loadWhatsAppStatuses() {
        // ✅ Check if we should skip Images/Videos refresh during media permission changes
        if (skipImagesVideosRefresh) {
            Log.d(TAG, "🚫 Skipping Images/Videos refresh during media permission change");
            return;
        }

        // Use repository for efficient loading
        repository.loadWhatsAppStatuses();
    }

    public void loadSavedMedia() {
        // Use repository for efficient loading
        repository.loadSavedMedia();
    }

    /**
     * ✅ Set flag to prevent Images/Videos tabs from refreshing during media permission changes
     */
    public void setSkipImagesVideosRefresh(boolean skip) {
        this.skipImagesVideosRefresh = skip;
        Log.d(TAG, "Skip Images/Videos refresh set to: " + skip);
    }

    /**
     * ✅ Load only Saved tab (for media permission changes)
     */
    public void loadSavedMediaOnly() {
        Log.d(TAG, "Loading ONLY Saved tab (Images/Videos tabs unchanged)");
        repository.loadSavedMedia();
    }

    public void onSAFAccessGranted(String treeUriKey) {
        Log.i(TAG, "SAF Access granted for key: " + treeUriKey + ". Reloading WhatsApp statuses.");
        loadWhatsAppStatuses();
    }
    
    public void resetSafAccessPromptNeeded() {
        isSafAccessPromptNeeded.setValue(false);
    }

    public void showToast(String message) {
        toastMessage.setValue(message);
    }


} 
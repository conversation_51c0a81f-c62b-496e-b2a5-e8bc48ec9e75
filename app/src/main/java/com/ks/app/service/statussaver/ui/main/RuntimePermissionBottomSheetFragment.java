package com.ks.app.service.statussaver.ui.main;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.ks.app.service.statussaver.R;

public class RuntimePermissionBottomSheetFragment extends BottomSheetDialogFragment {

    public static final String TAG = "RuntimePermissionBottomSheetFragment";

    private RuntimePermissionListener listener;

    public interface RuntimePermissionListener {
        void onGoToSettings();
        void onCancelRuntimePermission();
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof RuntimePermissionListener) {
            listener = (RuntimePermissionListener) context;
        } else {
            throw new RuntimeException(context.toString() + " must implement RuntimePermissionListener");
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // Set custom dark theme for the permission bottom sheet dialog
        setStyle(STYLE_NORMAL, R.style.Theme_PermissionBottomSheetDialog);
        // Prevent dismissal by outside touch
        setCancelable(false);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.bottom_sheet_runtime_permission, container, false);

        Button buttonSettings = view.findViewById(R.id.button_settings_runtime);
        Button buttonCancel = view.findViewById(R.id.button_cancel_runtime);

        buttonSettings.setOnClickListener(v -> {
            if (listener != null) {
                listener.onGoToSettings();
            }
            dismiss();
        });

        buttonCancel.setOnClickListener(v -> {
            if (listener != null) {
                listener.onCancelRuntimePermission();
            }
            dismiss();
        });

        return view;
    }

    @Override
    public void onDetach() {
        super.onDetach();
        listener = null;
    }
} 
package com.ks.app.service.statussaver.ui.main;

// Animation imports removed - no zoom animations
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.utils.SAFUtils;
import com.ks.app.service.statussaver.utils.PermissionUtils;


public class SafPermissionActivity extends AppCompatActivity {

    private static final String TAG = "SafPermissionActivity";
    // Animation variables removed - no zoom animations

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // ✅ Version check: This activity should only be used for Android 11+
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            Log.w(TAG, "⚠️ SafPermissionActivity launched on Android " + Build.VERSION.SDK_INT + " (< 30). This should only be used for Android 11+. Finishing activity.");
            finishWithSuccess(); // Finish with success since older versions don't need SAF
            return;
        }

        // Setup status bar to match background
        setupStatusBar();

        setContentView(R.layout.activity_saf_permission);

        Log.d(TAG, "🔐 Android 11+: SAF Permission screen - Checking if permissions are already granted");

        // Check if permissions are already granted
        if (checkIfPermissionsAlreadyGranted()) {
            Log.d(TAG, "✅ PERMISSIONS ALREADY GRANTED: Navigating directly to MainActivity");
            navigateToMainActivity();
            return;
        }

        Log.d(TAG, "❌ PERMISSIONS NEEDED: Showing permission interface");

        // Simple SAF permission activity - no overlays or guidance

        // Initialize views - only Continue button is functional
        Button continueButton = findViewById(R.id.button_allow_access);
        Button useFolderButton = findViewById(R.id.btnUseFolder);

        // Disable "Use this Folder" button - not touchable
        if (useFolderButton != null) {
            useFolderButton.setEnabled(false);
            useFolderButton.setClickable(false);
            useFolderButton.setFocusable(false);
            Log.d(TAG, "🚫 USE FOLDER BUTTON: Disabled and not touchable");
        }

        // Setup button click listener - only Continue button action
        continueButton.setOnClickListener(v -> {
            Log.d(TAG, "Continue button clicked");
            handleGrantAccess();
        });

        // Animation removed - no zoom animations
    }

    /**
     * ✅ Setup Status Bar to Match Background
     */
    private void setupStatusBar() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                Window window = getWindow();
                if (window != null) {
                    Log.d(TAG, "🎨 SETTING STATUS BAR: Dark background to match screen");

                    // Enable drawing system bar backgrounds
                    window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

                    // Set status bar and navigation bar colors to match screen background
                    window.setStatusBarColor(android.graphics.Color.parseColor("#101B23"));
                    window.setNavigationBarColor(android.graphics.Color.parseColor("#101B23"));

                    // Set light icons for dark background (Android 6.0+)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        View decorView = window.getDecorView();
                        int flags = decorView.getSystemUiVisibility();

                        // Remove light status bar flag for light icons on dark background
                        flags &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;

                        // Remove light navigation bar flag for light icons (Android 8.0+)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            flags &= ~View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR;
                        }

                        decorView.setSystemUiVisibility(flags);
                    }

                    Log.d(TAG, "✅ STATUS BAR SET: Dark background (#101B23) with light icons");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ ERROR SETTING STATUS BAR: " + e.getMessage(), e);
        }
    }

    /**
     * ✅ Handle Grant Access Button Click
     * Simply opens system file picker directly
     */
    /**
     * ✅ UNIVERSAL GRANT ACCESS - Handle folder access based on Android version
     */
    private void handleGrantAccess() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ (API 29+) - SAF is mandatory for cross-app media access
                Log.d(TAG, "📁 Android 10+ (API " + Build.VERSION.SDK_INT + "): Opening system file picker for SAF access");
                SAFUtils.requestSAFAccess(this);
            } else {
                // Android 4.4-9 (API 19-28) - File access is automatic with runtime permissions
                Log.d(TAG, "📁 Android 4.4-9 (API " + Build.VERSION.SDK_INT + "): File access automatic with runtime permissions");
                finishWithSuccess();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error opening file picker: " + e.getMessage(), e);
            finishWithError();
        }
    }

    /**
     * ✅ Check if Permissions are Already Granted
     * Determines if user has already granted necessary permissions
     */
    private boolean checkIfPermissionsAlreadyGranted() {
        try {
            // For Android 11+ (API 30+), check SAF (Storage Access Framework) permissions
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                boolean hasValidSafAccess = false;
                try {
                    hasValidSafAccess = SAFUtils.hasWhatsAppStatusAccess(this);
                    Log.d(TAG, "📱 Android 11+: SAF Access Status: " + hasValidSafAccess);
                } catch (Exception e) {
                    Log.w(TAG, "⚠️ Error checking SAF access: " + e.getMessage());
                    hasValidSafAccess = false;
                }
                return hasValidSafAccess;
            } else {
                // For Android 6-10, check runtime permissions (Android 10 uses legacy mode)
                boolean hasRuntimePermissions = false;
                try {
                    hasRuntimePermissions = PermissionUtils.hasPermissions(this);
                    Log.d(TAG, "📱 Android 6-10: Runtime Permissions Status: " + hasRuntimePermissions + " (Android 10 with legacy mode)");
                } catch (Exception e) {
                    Log.w(TAG, "⚠️ Error checking runtime permissions: " + e.getMessage());
                    hasRuntimePermissions = false;
                }
                return hasRuntimePermissions;
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ ERROR CHECKING PERMISSIONS: " + e.getMessage(), e);
            return false; // Show permission screen as fallback
        }
    }

    /**
     * ✅ Navigate to MainActivity
     * Common method for navigating to main activity
     */
    private void navigateToMainActivity() {
        Log.d(TAG, "✅ NAVIGATING TO MAIN ACTIVITY");
        Log.d(TAG, "🏠 FLOW: Permission Screen → Home Screen");

        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    /**
     * ✅ Finish Activity with Success Result and Navigate to MainActivity
     */
    private void finishWithSuccess() {
        Log.d(TAG, "✅ PERMISSION GRANTED: User successfully granted permissions");
        navigateToMainActivity();
    }

    /**
     * ✅ Finish Activity with Error Result
     */
    private void finishWithError() {
        Log.d(TAG, "❌ PERMISSION DENIED: User denied folder access");
        Log.d(TAG, "🔙 FLOW: Permission Screen → Exit App");

        // For now, just finish the activity
        // You could also navigate to MainActivity with limited functionality
        Intent resultIntent = new Intent();
        resultIntent.putExtra("permission_granted", false);
        setResult(RESULT_CANCELED, resultIntent);
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == SAFUtils.REQUEST_CODE_SAF_WHATSAPP_STATUSES) {
            if (resultCode == RESULT_OK && data != null) {
                try {
                    // Handle SAF permission result
                    SAFUtils.persistFolderUri(this, data, SAFUtils.getWhatsAppStatusesTreeUriKey());
                    Log.d(TAG, "✅ SAF PERMISSION GRANTED: User successfully granted folder access");
                    finishWithSuccess();
                } catch (Exception e) {
                    Log.e(TAG, "Error handling SAF result: " + e.getMessage(), e);
                    finishWithError();
                }
            } else {
                Log.d(TAG, "❌ SAF PERMISSION DENIED: User cancelled or pressed back in folder picker");
                // Don't finish the activity - stay on permission screen
                // Will check permissions again in onResume
            }
        }
    }



    // Zoom animation method removed - no animations

    /**
     * ✅ Show Simple Guidance Dialog
     * Quick instruction before opening file picker
     */
    /**
     * ✅ UNIVERSAL GUIDANCE DIALOG - Works for all Android 10+ devices
     */
    private void showSimpleGuidanceDialog() {
        try {
            String title = "📁 Media Folder Access Required";
            String message = "To access WhatsApp statuses across all devices:\n\n" +
                           "1. Navigate to: Android → media\n" +
                           "2. Tap 'USE THIS FOLDER' 👆\n\n" +
                           "This works on all Android 10+ devices and is 100% Play Store compliant.";

            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton("Open File Picker", (dialog, which) -> {
                    Log.d(TAG, "✅ USER READY: Opening universal file picker");
                    SAFUtils.requestSAFAccess(this);
                })
                .setNegativeButton("Cancel", (dialog, which) -> {
                    Log.d(TAG, "❌ USER CANCELLED: File picker not opened");
                })
                .setCancelable(false)
                .show();

            Log.d(TAG, "🎯 UNIVERSAL GUIDANCE SHOWN: User sees Android 10+ instruction");

        } catch (Exception e) {
            Log.e(TAG, "Error showing guidance dialog: " + e.getMessage(), e);
            // Fallback - open file picker directly
            SAFUtils.requestSAFAccess(this);
        }
    }



    // All guidance and overlay methods removed - simple direct file picker access


    @Override
    protected void onDestroy() {
        super.onDestroy();

        Log.d(TAG, "🧹 ACTIVITY DESTROY: Cleaning up all resources");

        // Simple cleanup - no overlays, hints, or animations
    }

    @Override
    protected void onResume() {
        super.onResume();

        Log.d(TAG, "🔄 ON RESUME: Checking permissions after returning from folder picker");

        // Check if permissions were granted while we were away (e.g., from folder picker)
        if (checkIfPermissionsAlreadyGranted()) {
            Log.d(TAG, "✅ PERMISSIONS GRANTED ON RESUME: Navigating to MainActivity");
            navigateToMainActivity();
        } else {
            Log.d(TAG, "❌ PERMISSIONS NOT GRANTED: Staying on permission screen");
        }
    }

    @Override
    public void onBackPressed() {
        // Simple back button handling
        Log.d(TAG, "🔙 BACK PRESSED: Finishing activity");
        finishWithError();
    }
}

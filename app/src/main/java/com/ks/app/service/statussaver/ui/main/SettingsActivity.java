package com.ks.app.service.statussaver.ui.main;

import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListView;
import android.widget.Toast;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import com.ks.app.service.statussaver.R;
import android.widget.TextView;
import android.widget.ImageView;
import android.os.Build;
import android.content.Intent;
import com.ks.app.service.statussaver.ui.base.BaseActivity;

public class SettingsActivity extends BaseActivity {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        setTheme(R.style.Theme_SettingsStatusBar);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        ListView listView = findViewById(R.id.settings_list);

        // ✅ Settings items with proper styling
        String[] items = {"Privacy Policy", "Contact Us", "About"};
        int[] icons = {
            R.drawable.ic_privacy_policy,
            R.drawable.ic_contact_us,
            R.drawable.ic_about
        };

        // ✅ Use custom adapter with borders and proper styling
        SettingsAdapter adapter = new SettingsAdapter(this, items, icons);
        listView.setAdapter(adapter);

        // ✅ Set list styling for better visibility
        listView.setDivider(null); // We handle dividers in the custom layout
        listView.setDividerHeight(0);
        listView.setSelector(android.R.color.transparent); // Prevent default selection color

        listView.setOnItemClickListener((parent, view, position, id) -> {
            Intent intent;
            switch (position) {
                case 0: // Privacy Policy
                    intent = new Intent(this, com.ks.app.service.statussaver.ui.settings.PrivacyPolicyActivity.class);
                    startActivity(intent);
                    break;
                case 1: // Contact Us
                    intent = new Intent(this, com.ks.app.service.statussaver.ui.settings.ContactUsActivity.class);
                    startActivity(intent);
                    break;
                case 2: // About
                    intent = new Intent(this, com.ks.app.service.statussaver.ui.settings.AboutActivity.class);
                    startActivity(intent);
                    break;
            }
        });

        ImageView backIcon = findViewById(R.id.back_icon);
        backIcon.setImageResource(R.drawable.ic_arrow_back_24);
        backIcon.setOnClickListener(v -> finish());
    }
} 
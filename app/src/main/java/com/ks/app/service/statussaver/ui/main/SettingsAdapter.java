package com.ks.app.service.statussaver.ui.main;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import com.ks.app.service.statussaver.R;

/**
 * Custom adapter for settings list with proper borders and styling
 */
public class SettingsAdapter extends BaseAdapter {

    private Context context;
    private String[] items;
    private int[] icons;
    private LayoutInflater inflater;

    public SettingsAdapter(Context context, String[] items, int[] icons) {
        this.context = context;
        this.items = items;
        this.icons = icons;
        this.inflater = LayoutInflater.from(context);
    }

    @Override
    public int getCount() {
        return items.length;
    }

    @Override
    public Object getItem(int position) {
        return items[position];
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;

        if (convertView == null) {
            convertView = inflater.inflate(R.layout.settings_list_item, parent, false);
            holder = new ViewHolder();
            holder.icon = convertView.findViewById(R.id.item_icon);
            holder.text = convertView.findViewById(R.id.item_text);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        // Set the text
        holder.text.setText(items[position]);
        
        // Set the icon
        if (icons != null && position < icons.length) {
            holder.icon.setImageResource(icons[position]);
        }

        // Ensure proper styling
        holder.text.setTextColor(context.getResources().getColor(android.R.color.white, context.getTheme()));

        return convertView;
    }

    private static class ViewHolder {
        ImageView icon;
        TextView text;
    }
}

package com.ks.app.service.statussaver.ui.settings;

import android.os.Bundle;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.ks.app.service.statussaver.R;

/**
 * About Activity - Clean version without email functionality
 */
public class AboutActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setTheme(R.style.Theme_SettingsStatusBar);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_about);

        setupBackButton();
        setupContent();
    }

    private void setupBackButton() {
        ImageView backIcon = findViewById(R.id.back_icon);
        backIcon.setImageResource(R.drawable.ic_arrow_back_24);
        backIcon.setOnClickListener(v -> finish());
    }

    private void setupContent() {
        TextView contentText = findViewById(R.id.content_text);
        TextView emailText = findViewById(R.id.email_text);

        String aboutContent = "SavePro – Status Downloader is a utility app designed to help users view and save status videos and images directly from their device. With a focus on privacy and performance, the app ensures that all media files remain stored locally and are never uploaded or shared externally.\n\n" +
                "Key Features:\n\n" +
                "• Save status images and videos from your device\n\n" +
                "• Simple and user-friendly interface\n\n" +
                "• No personal login or account required\n\n" +
                "• Minimal permissions and no personal data collection\n\n" +
                "• 100% local media access and storage\n\n" +
                "• Supports ads to keep the app free for users\n\n" +
                "SavePro prioritizes your privacy while delivering essential functionality for managing and saving status updates easily and efficiently.";

        contentText.setText(aboutContent);

        // Remove email section completely - no email display or functionality needed
        emailText.setVisibility(android.view.View.GONE);
    }
}

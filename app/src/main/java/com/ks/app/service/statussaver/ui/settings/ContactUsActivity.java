package com.ks.app.service.statussaver.ui.settings;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.ui.base.BaseActivity;

/**
 * Contact Us Activity
 */
public class ContactUsActivity extends BaseActivity {

    private static final String CONTACT_EMAIL = "<EMAIL>";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setTheme(R.style.Theme_SettingsStatusBar);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_contact_us);

        setupBackButton();
        setupContent();
    }

    private void setupBackButton() {
        ImageView backIcon = findViewById(R.id.back_icon);
        backIcon.setImageResource(R.drawable.ic_arrow_back_24);
        backIcon.setOnClickListener(v -> finish());
    }

    private void setupContent() {
        TextView contentText = findViewById(R.id.content_text);
        TextView emailText = findViewById(R.id.email_text);

        String contactContent = "If you have any questions, feedback, or support inquiries, please contact us:\n\n" +
                "Developer Email:\n" +
                "📧 " + CONTACT_EMAIL + "\n\n" +
                "We are committed to addressing your concerns promptly and continuously improving the user experience.";

        contentText.setText(contactContent);

        // Hide the separate email text view since email is now in the main content
        emailText.setVisibility(android.view.View.GONE);
    }

    private void openEmailClient() {
        try {
            Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
            emailIntent.setData(Uri.parse("mailto:" + CONTACT_EMAIL));
            emailIntent.putExtra(Intent.EXTRA_SUBJECT, "SavePro – Status Downloader - Support Request");
            emailIntent.putExtra(Intent.EXTRA_TEXT, "Hello,\n\nI have a question about the SavePro – Status Downloader app:\n\n");
            
            if (emailIntent.resolveActivity(getPackageManager()) != null) {
                startActivity(Intent.createChooser(emailIntent, "Send Email"));
            } else {
                // Fallback: copy email to clipboard
                android.content.ClipboardManager clipboard = (android.content.ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
                android.content.ClipData clip = android.content.ClipData.newPlainText("Email", CONTACT_EMAIL);
                clipboard.setPrimaryClip(clip);
                Toast.makeText(this, "Email copied to clipboard: " + CONTACT_EMAIL, Toast.LENGTH_LONG).show();
            }
        } catch (Exception e) {
            // Fallback: copy email to clipboard
            android.content.ClipboardManager clipboard = (android.content.ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
            android.content.ClipData clip = android.content.ClipData.newPlainText("Email", CONTACT_EMAIL);
            clipboard.setPrimaryClip(clip);
            Toast.makeText(this, "Email copied to clipboard: " + CONTACT_EMAIL, Toast.LENGTH_LONG).show();
        }
    }
}

package com.ks.app.service.statussaver.ui.splash;

import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.animation.AlphaAnimation;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.ui.base.BaseActivity;
import com.ks.app.service.statussaver.utils.NavigationHelper;

/**
 * ✅ Optimized Splash Activity - Clean Architecture
 * Handles 4-second splash with background ad loading
 * Uses NavigationHelper for smart permission-based navigation
 * No in-app update logic (handled globally by BaseActivity)
 */
public class SplashActivity extends BaseActivity {

    private static final String TAG = "SplashActivity";
    private static final int SPLASH_DURATION = 4000; // 4 seconds - minimum splash time

    // Production interstitial ad unit ID
    private static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-7557152164205920/6418571631";

    // Ad management
    private InterstitialAd mInterstitialAd;
    private boolean adLoaded = false;
    private boolean adLoadingCompleted = false;
    private boolean adShown = false;
    
    // Timer management
    private boolean splashTimerFinished = false;
    private boolean navigationInProgress = false;
    
    // Handler for timing
    private Handler mainHandler;
    private Runnable splashTimerRunnable;
    
    // Ad loading tracking
    private long adLoadStartTime = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        Log.d(TAG, "🚀 OPTIMIZED SPLASH: Starting 4-second splash with background ad loading");
        Log.d(TAG, "🔍 UPDATE HANDLING: Managed globally by BaseActivity");

        // Set status bar color to match splash background
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setStatusBarColor(getResources().getColor(R.color.splash_background, getTheme()));
        }

        // Hide the action bar for full screen splash
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        // Initialize views and animations
        initializeViewsAndAnimations();

        // Initialize handler
        mainHandler = new Handler(Looper.getMainLooper());

        // Initialize Mobile Ads SDK
        MobileAds.initialize(this, initializationStatus -> {
            Log.d(TAG, "✅ AdMob SDK initialized - ready for background ad loading");
        });

        // Start the optimized splash flow
        startOptimizedSplashFlow();
    }

    /**
     * ✅ Initialize Views and Animations
     */
    private void initializeViewsAndAnimations() {
        // Initialize views
        ImageView splashIcon = findViewById(R.id.splash_icon);
        TextView appName = findViewById(R.id.splash_app_name);
        ProgressBar loadingIndicator = findViewById(R.id.splash_loading_indicator);

        // Create fade-in animation
        AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
        fadeIn.setDuration(1000); // 1 second fade-in
        fadeIn.setStartOffset(200); // Start after 200ms

        // Apply animations
        splashIcon.startAnimation(fadeIn);
        appName.startAnimation(fadeIn);
        loadingIndicator.startAnimation(fadeIn);

        Log.d(TAG, "🎭 ANIMATIONS: Splash animations started");
    }

    /**
     * ✅ Start Optimized Splash Flow
     * Handles 4-second timer and background ad loading
     */
    private void startOptimizedSplashFlow() {
        Log.d(TAG, "⏰ SPLASH TIMER: Starting 4-second countdown");
        
        // Start background ad loading immediately
        loadInterstitialAdInBackground();

        // Create and start 4-second timer
        splashTimerRunnable = new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "⏰ SPLASH TIMER: 4 seconds completed");
                splashTimerFinished = true;
                checkAdStatusAndProceed();
            }
        };

        // Start the 4-second timer
        mainHandler.postDelayed(splashTimerRunnable, SPLASH_DURATION);
    }

    /**
     * ✅ Check Ad Status and Proceed
     * Called after 4-second timer completes
     */
    private void checkAdStatusAndProceed() {
        if (navigationInProgress) {
            Log.d(TAG, "🚫 NAVIGATION IN PROGRESS: Skipping duplicate navigation");
            return;
        }

        Log.d(TAG, "📊 AD STATUS CHECK: Timer finished, checking ad status");
        Log.d(TAG, "   📺 Ad Loaded: " + adLoaded);
        Log.d(TAG, "   🔄 Ad Loading Completed: " + adLoadingCompleted);

        if (adLoaded && mInterstitialAd != null) {
            // Ad loaded successfully - show it
            Log.d(TAG, "✅ AD READY: Showing interstitial ad");
            showInterstitialAd();
        } else if (adLoadingCompleted) {
            // Ad failed to load - skip and proceed
            Log.d(TAG, "❌ AD FAILED: Skipping ad and proceeding to navigation");
            proceedToNavigation();
        } else {
            // Ad still loading - wait for it
            Log.d(TAG, "⏳ AD STILL LOADING: Waiting for ad to complete");
            // The ad callback will handle navigation when ready
        }
    }

    /**
     * ✅ Load Interstitial Ad in Background
     * Starts loading immediately during splash screen
     */
    private void loadInterstitialAdInBackground() {
        adLoadStartTime = System.currentTimeMillis();
        Log.d(TAG, "🚀 BACKGROUND AD LOADING: Starting interstitial ad load");
        Log.d(TAG, "📱 Ad Unit ID: " + INTERSTITIAL_AD_UNIT_ID);

        // Create ad request
        AdRequest adRequest = new AdRequest.Builder().build();

        // Start loading ad in background
        InterstitialAd.load(this, INTERSTITIAL_AD_UNIT_ID, adRequest,
            new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.d(TAG, "✅ AD LOADED: Successfully loaded in " + loadTime + "ms");

                    mInterstitialAd = interstitialAd;
                    adLoaded = true;
                    adLoadingCompleted = true;

                    // If timer already finished, proceed immediately
                    if (splashTimerFinished) {
                        Log.d(TAG, "⏰ TIMER ALREADY FINISHED: Proceeding to show ad");
                        checkAdStatusAndProceed();
                    } else {
                        Log.d(TAG, "⏳ WAITING FOR TIMER: Ad ready, waiting for 4-second timer");
                    }
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError adError) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.w(TAG, "❌ AD FAILED: Failed to load after " + loadTime + "ms");
                    Log.w(TAG, "📊 Error: " + adError.getMessage());

                    mInterstitialAd = null;
                    adLoaded = false;
                    adLoadingCompleted = true;

                    // If timer already finished, proceed immediately
                    if (splashTimerFinished) {
                        Log.d(TAG, "⏰ TIMER ALREADY FINISHED: Proceeding without ad");
                        checkAdStatusAndProceed();
                    } else {
                        Log.d(TAG, "⏳ WAITING FOR TIMER: Ad failed, waiting for 4-second timer");
                    }
                }
            });
    }

    /**
     * ✅ Show Interstitial Ad
     * Displays the loaded ad with proper callbacks
     */
    private void showInterstitialAd() {
        if (mInterstitialAd == null || adShown || navigationInProgress) {
            Log.w(TAG, "⚠️ CANNOT SHOW AD: Ad null, already shown, or navigation in progress");
            proceedToNavigation();
            return;
        }

        Log.d(TAG, "📺 SHOWING AD: Displaying interstitial ad to user");
        adShown = true;
        navigationInProgress = true;

        // Set up ad callbacks
        mInterstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "✅ AD DISMISSED: User closed ad, proceeding to navigation");
                mInterstitialAd = null;
                proceedToNavigation();
            }

            @Override
            public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                Log.w(TAG, "❌ AD SHOW FAILED: " + adError.getMessage());
                mInterstitialAd = null;
                proceedToNavigation();
            }

            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "📺 AD DISPLAYED: Interstitial ad is now showing");
            }
        });

        // Show the ad
        mInterstitialAd.show(this);
    }

    /**
     * ✅ Proceed to Navigation
     * Uses NavigationHelper for smart permission-based navigation
     */
    private void proceedToNavigation() {
        if (navigationInProgress && !adShown) {
            Log.d(TAG, "🚫 NAVIGATION ALREADY IN PROGRESS: Preventing duplicate navigation");
            return;
        }

        navigationInProgress = true;
        Log.d(TAG, "🔍 SMART NAVIGATION: Using NavigationHelper for permission-based routing");

        // Clean up resources
        cleanupResources();

        // Use NavigationHelper for smart navigation
        NavigationHelper.checkPermissionsAndNavigate(this);
    }

    /**
     * ✅ Clean Up Resources
     * Cleans up handlers and ad resources before navigation
     */
    private void cleanupResources() {
        Log.d(TAG, "🧹 CLEANUP: Cleaning up splash screen resources");

        // Clean up handler
        if (mainHandler != null && splashTimerRunnable != null) {
            mainHandler.removeCallbacks(splashTimerRunnable);
        }

        // Clean up ad
        if (mInterstitialAd != null) {
            mInterstitialAd = null;
        }

        Log.d(TAG, "✅ CLEANUP COMPLETE: All resources cleaned up");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "💀 ACTIVITY DESTROYED: Cleaning up resources");
        cleanupResources();
    }

    @Override
    public void onBackPressed() {
        // Disable back button during splash screen
        Log.d(TAG, "🚫 BACK BUTTON DISABLED: Please wait for splash to complete");
        // Do nothing - prevent user from interrupting the flow
    }
}

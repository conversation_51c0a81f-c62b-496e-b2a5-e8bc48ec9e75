package com.ks.app.service.statussaver.ui.splash;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.ui.main.MainActivity;
import com.ks.app.service.statussaver.ui.main.SafPermissionActivity;
import com.ks.app.service.statussaver.utils.SAFUtils;
import com.ks.app.service.statussaver.utils.PermissionUtils;
import com.ks.app.service.statussaver.utils.InAppUpdateHelper;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.AdError;
import androidx.annotation.NonNull;



public class SplashActivity extends AppCompatActivity {

    private static final String TAG = "SplashActivity";
    private static final int SPLASH_DURATION = 4000; // 4 seconds - minimum splash time
    private static final int MAX_AD_WAIT_TIME = 9000; // Maximum 9 seconds total wait for ad

    // Your production interstitial ad unit ID
    private static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-7557152164205920/6418571631";

    private InterstitialAd mInterstitialAd;
    private boolean splashTimerFinished = false;
    private boolean adLoaded = false;
    private boolean adLoadingCompleted = false; // Track if ad loading finished (success or failure)
    private boolean adShown = false;
    private boolean adFullyViewed = false; // Track if ad was fully viewed by user
    private boolean navigationInProgress = false; // Prevent multiple navigation attempts
    private long adLoadStartTime = 0;

    // In-app update tracking
    private InAppUpdateHelper updateHelper;
    private boolean updateCheckCompleted = false;
    private boolean updateAvailable = false;

    // Handler references for proper cleanup
    private Handler mainHandler;
    private Runnable splashTimerRunnable;
    private Runnable timeoutRunnable;

    // Ad callback references for cleanup
    private FullScreenContentCallback adContentCallback;

    // Status bar state tracking
    private boolean originalStatusBarState = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        // Set status bar color to match splash background
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setStatusBarColor(getResources().getColor(R.color.splash_background, getTheme()));
        }

        // Hide the action bar for full screen splash
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        // Initialize views
        ImageView splashIcon = findViewById(R.id.splash_icon);
        TextView appName = findViewById(R.id.splash_app_name);
        ProgressBar loadingIndicator = findViewById(R.id.splash_loading_indicator);

        // Create fade-in animation
        AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
        fadeIn.setDuration(1000); // 1 second fade-in
        fadeIn.setStartOffset(200); // Start after 200ms

        // Apply animations
        splashIcon.startAnimation(fadeIn);
        appName.startAnimation(fadeIn);
        loadingIndicator.startAnimation(fadeIn);

        Log.d(TAG, "🚀 NEW FLOW: Starting 4-second splash with background ad loading");

        // ✅ Initialize Handler for proper cleanup
        mainHandler = new Handler(Looper.getMainLooper());

        // ✅ Initialize In-App Update Helper
        updateHelper = new InAppUpdateHelper(this);
        Log.d(TAG, "🔄 InAppUpdateHelper initialized for update checks");

        // ✅ Start in-app update check immediately during splash
        startInAppUpdateCheck();

        // ✅ Initialize Mobile Ads SDK immediately for background loading
        MobileAds.initialize(this, initializationStatus -> {
            Log.d(TAG, "✅ AdMob SDK initialized - ready for background ad loading");
        });

        // ✅ Start loading interstitial ad in background immediately
        Log.d(TAG, "🔄 Starting background interstitial ad loading during 4-second splash...");
        loadInterstitialAdInBackground();

        // ✅ Create splash timer runnable
        splashTimerRunnable = new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "⏰ SPLASH FLOW: Minimum splash time (4s) completed");
                splashTimerFinished = true;
                // Check if both async conditions are met and we can proceed
                checkBothConditionsAndProceed();
            }
        };

        // ✅ Create timeout runnable
        timeoutRunnable = new Runnable() {
            @Override
            public void run() {
                if (!adLoadingCompleted) {
                    Log.w(TAG, "⏰ TIMEOUT: Maximum wait time reached - forcing ad loading completion");
                    Log.w(TAG, "🚨 EMERGENCY FALLBACK: Proceeding without ad after " + MAX_AD_WAIT_TIME + "ms");

                    // Force completion and proceed
                    adLoadingCompleted = true;
                    adLoaded = false;
                    mInterstitialAd = null;

                    // FAIL-SAFE TIMEOUT: Force completion and proceed
                    Log.d(TAG, "⏰ SPLASH FLOW TIMEOUT: Maximum wait time reached - fail-safe behavior");

                    if (!updateCheckCompleted) {
                        Log.d(TAG, "⏰ TIMEOUT: Update check incomplete - treating as 'no update needed' (fail-safe)");
                        updateCheckCompleted = true;
                        updateAvailable = false; // Fail-safe: Allow flow to continue
                    }

                    if (!adLoadingCompleted) {
                        Log.d(TAG, "⏰ TIMEOUT: Ad loading incomplete - treating as 'failed' (fail-safe)");
                        adLoadingCompleted = true;
                        adLoaded = false; // Fail-safe: Allow flow to continue
                    }

                    Log.d(TAG, "⏰ TIMEOUT: Fail-safe completion - checking conditions to proceed");
                    checkBothConditionsAndProceed();
                } else {
                    Log.d(TAG, "⏰ TIMEOUT SKIPPED: Ad loading already completed, timeout not needed");
                }
            }
        };

        // ✅ Start timers with proper references for cleanup
        mainHandler.postDelayed(splashTimerRunnable, SPLASH_DURATION);
        mainHandler.postDelayed(timeoutRunnable, MAX_AD_WAIT_TIME);
    }

    /**
     * ✅ Load Interstitial Ad in Background - Optimized for Splash Screen
     * Starts loading immediately when splash screen appears for faster ad delivery
     */
    private void loadInterstitialAdInBackground() {
        adLoadStartTime = System.currentTimeMillis();
        Log.d(TAG, "🚀 BACKGROUND LOADING: Starting interstitial ad load during splash screen");
        Log.d(TAG, "📱 Ad Unit ID: " + INTERSTITIAL_AD_UNIT_ID);
        Log.d(TAG, "⏰ Load start time: " + adLoadStartTime);

        // Create ad request
        AdRequest adRequest = new AdRequest.Builder().build();

        // Start loading ad in background
        InterstitialAd.load(this, INTERSTITIAL_AD_UNIT_ID, adRequest,
            new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.d(TAG, "✅ AD LOADED SUCCESSFULLY: Interstitial ad loaded during background loading!");
                    Log.d(TAG, "⚡ Load time: " + loadTime + "ms (Target: within " + SPLASH_DURATION + "ms)");

                    mInterstitialAd = interstitialAd;
                    adLoaded = true;
                    adLoadingCompleted = true; // Mark ad loading as completed successfully

                    if (loadTime < SPLASH_DURATION) {
                        Log.d(TAG, "🚀 PERFECT TIMING: Ad loaded in " + loadTime + "ms (within 3-second window)");
                        Log.d(TAG, "⏳ WAITING FOR TIMER: Keeping splash visible until 3-second minimum...");
                    } else {
                        Log.d(TAG, "⏰ SLOW LOADING: Ad took " + loadTime + "ms (longer than 3 seconds)");
                        Log.d(TAG, "🎯 READY TO PROCEED: Will check if timer is also finished");
                    }

                    // STEP 3: Ad loaded successfully
                    Log.d(TAG, "✅ SPLASH FLOW STEP 3: Ad loaded successfully");
                    checkBothConditionsAndProceed();
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError adError) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.w(TAG, "❌ AD FAILED TO LOAD: Interstitial ad failed during background loading");
                    Log.w(TAG, "⏰ Failed after: " + loadTime + "ms");
                    Log.w(TAG, "📊 Error details: " + adError.getMessage());
                    Log.w(TAG, "🔢 Error code: " + adError.getCode());
                    Log.w(TAG, "🌐 Error domain: " + adError.getDomain());

                    mInterstitialAd = null;
                    adLoaded = false; // Ad failed to load
                    adLoadingCompleted = true; // Mark ad loading as completed (with failure)

                    Log.d(TAG, "⏳ WAITING FOR TIMER: Ad failed, but keeping splash visible until 3-second minimum");

                    // STEP 3: Ad failed to load - fail-safe behavior (still proceed)
                    Log.d(TAG, "❌ SPLASH FLOW STEP 3: Ad failed to load - fail-safe behavior (still proceed)");
                    checkBothConditionsAndProceed();
                }
            });
    }

    /**
     * ✅ Check Ad Status and Proceed - LEGACY METHOD (kept for compatibility)
     * This method is now primarily used as a fallback
     * The new flow uses checkForInAppUpdates() → proceedToAdDisplay()
     */
    private void checkAdStatusAndProceed() {
        Log.d(TAG, "🔍 LEGACY CHECK STATUS:");
        Log.d(TAG, "   📱 Splash timer finished: " + splashTimerFinished);
        Log.d(TAG, "   📺 Ad loaded successfully: " + adLoaded);
        Log.d(TAG, "   ✅ Ad loading completed: " + adLoadingCompleted);
        Log.d(TAG, "   🔄 Update check completed: " + updateCheckCompleted);

        // In new flow, we should have update check completed before reaching here
        if (!updateCheckCompleted) {
            Log.d(TAG, "⚠️ FALLBACK: Update check not completed, forcing completion");
            updateCheckCompleted = true;
        }

        if (adLoadingCompleted) {
            // Clear timeout and proceed
            Log.d(TAG, "🧹 CLEARING TIMEOUT: Ad loading completed, removing timeout handler");
            clearTimeoutHandler();

            if (adLoaded) {
                Log.d(TAG, "✅ CONDITIONS MET: Update check done AND ad loaded successfully");
                Log.d(TAG, "🎯 HIDING SPLASH: Now hiding splash screen and showing ad");
            } else {
                Log.d(TAG, "✅ CONDITIONS MET: Update check done AND ad loading completed (failed)");
                Log.d(TAG, "🎯 HIDING SPLASH: Now hiding splash screen and going to permission screen");
            }
            showInterstitialAdOrProceed();
        } else if (splashTimerFinished && !adLoadingCompleted) {
            // Timer finished but ad still loading - KEEP SPLASH VISIBLE
            Log.d(TAG, "⏳ WAITING FOR AD: 3 seconds passed, but ad still loading...");
            Log.d(TAG, "👁️ SPLASH STAYS VISIBLE: Keeping splash screen visible until ad loading completes");
            // Do nothing - keep waiting for ad loading to complete
        } else if (!splashTimerFinished && adLoadingCompleted) {
            // Ad loading completed but timer not finished - KEEP SPLASH VISIBLE
            if (adLoaded) {
                Log.d(TAG, "⏳ WAITING FOR TIMER: Ad loaded successfully, but waiting for 3-second minimum...");
            } else {
                Log.d(TAG, "⏳ WAITING FOR TIMER: Ad failed to load, but waiting for 3-second minimum...");
            }
            Log.d(TAG, "👁️ SPLASH STAYS VISIBLE: Keeping splash screen visible until timer completes");
            // Do nothing - keep waiting for timer
        } else {
            // Neither condition met - KEEP SPLASH VISIBLE
            Log.d(TAG, "⏳ WAITING FOR BOTH: Timer and ad loading both still in progress...");
            Log.d(TAG, "👁️ SPLASH STAYS VISIBLE: Waiting for both conditions to be met");
        }
    }

    /**
     * ✅ Show Interstitial Ad or Proceed - STRICT AD DISPLAY POLICY
     * ALWAYS shows ad if loaded successfully - NEVER skips unless ad failed to load
     * Only proceeds to permission screen after ad is FULLY VIEWED or if ad loading failed
     * Includes comprehensive ad resource cleanup before navigation
     */
    private void showInterstitialAdOrProceed() {
        Log.d(TAG, "🏁 READY TO PROCEED: Both splash time and ad loading are complete!");

        if (mInterstitialAd != null && !adShown && !navigationInProgress) {
            Log.d(TAG, "🎯 MANDATORY AD DISPLAY: Ad loaded successfully - MUST be shown to user");
            Log.d(TAG, "🚫 NO SKIPPING: Ad will be displayed and user must view it completely");
            Log.d(TAG, "👁️ FULL VIEWING REQUIRED: User must interact with ad before proceeding");
            Log.d(TAG, "⏳ Waiting for user to fully view and close ad before navigating...");

            adShown = true; // Mark ad as shown to prevent multiple displays

            // Create and store callback reference for cleanup
            adContentCallback = new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    Log.d(TAG, "✅ USER DISMISSED AD: User fully viewed and dismissed interstitial ad");
                    Log.d(TAG, "👁️ AD FULLY VIEWED: User has completed viewing the ad");

                    adFullyViewed = true; // Mark ad as fully viewed

                    // Restore status bar to original state
                    Log.d(TAG, "🎨 RESTORING STATUS BAR: After ad dismissal");
                    restoreStatusBar();

                    Log.d(TAG, "🧹 STARTING COMPLETE AD CLEANUP: Clearing all ad resources, callbacks, and listeners");
                    performCompleteAdCleanup();

                    Log.d(TAG, "🔐 NAVIGATING TO PERMISSION SCREEN: Going to SafPermissionActivity after full ad viewing");
                    navigateToPermissionScreen();
                }

                @Override
                public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                    Log.w(TAG, "❌ FAILED TO SHOW AD: " + adError.getMessage());
                    Log.w(TAG, "🚨 AD DISPLAY ERROR: Ad failed to display after loading successfully");

                    // Restore status bar if it was changed
                    Log.d(TAG, "🎨 RESTORING STATUS BAR: After ad display failure");
                    restoreStatusBar();

                    Log.d(TAG, "🧹 STARTING COMPLETE AD CLEANUP: Clearing all ad resources after display failure");
                    performCompleteAdCleanup();

                    Log.d(TAG, "🔐 NAVIGATING TO PERMISSION SCREEN: Going to SafPermissionActivity without ad");
                    navigateToPermissionScreen();
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    Log.d(TAG, "📺 AD DISPLAYED: Interstitial ad is now showing to user");
                    Log.d(TAG, "👁️ FULL VIEWING STARTED: User must view ad completely before proceeding");

                    // Set status bar to black during ad display
                    Log.d(TAG, "🎨 SETTING STATUS BAR BLACK: For optimal ad viewing experience");
                    setStatusBarBlack();

                    Log.d(TAG, "⏳ Waiting for user to fully view and dismiss ad...");
                }

                @Override
                public void onAdClicked() {
                    Log.d(TAG, "👆 USER CLICKED AD: User interacted with the ad");
                    Log.d(TAG, "💰 AD ENGAGEMENT: User clicked - good for revenue");
                    // Note: Don't navigate here - wait for dismiss to ensure full viewing
                }

                @Override
                public void onAdImpression() {
                    Log.d(TAG, "👁️ AD IMPRESSION: Ad impression recorded for revenue");
                    Log.d(TAG, "📊 MONETIZATION: Ad view counted successfully");
                }
            };

            // Set the callback to the ad
            mInterstitialAd.setFullScreenContentCallback(adContentCallback);

            // Show the ad that was loaded in background
            Log.d(TAG, "🚀 DISPLAYING AD: Showing interstitial ad to user now");
            Log.d(TAG, "🎯 MANDATORY VIEWING: User must view ad completely before proceeding");
            mInterstitialAd.show(this);

        } else if (adShown) {
            Log.d(TAG, "⚠️ AD ALREADY SHOWN: Interstitial ad was already displayed");
            Log.d(TAG, "🧹 PERFORMING CLEANUP: Ensuring all ad resources are cleared");
            performCompleteAdCleanup();
            Log.d(TAG, "🔐 NAVIGATING TO PERMISSION SCREEN: Going to SafPermissionActivity");
            navigateToPermissionScreen();

        } else if (navigationInProgress) {
            Log.d(TAG, "🚫 NAVIGATION IN PROGRESS: Already navigating, preventing duplicate navigation");

        } else {
            Log.d(TAG, "❌ AD LOADING FAILED: No interstitial ad to show (loading failed during splash)");
            Log.d(TAG, "🚫 SKIP REASON: Ad failed to load - this is the ONLY reason we skip ads");

            Log.d(TAG, "🧹 PERFORMING COMPLETE CLEANUP: Clearing all ad resources after load failure");
            performCompleteAdCleanup();

            Log.d(TAG, "🔐 NAVIGATING TO PERMISSION SCREEN: Going directly to SafPermissionActivity");
            navigateToPermissionScreen();
        }
    }

    /**
     * ✅ Perform Complete Ad Cleanup - COMPREHENSIVE RESOURCE CLEANUP
     * Clears ALL ad-related resources, callbacks, listeners, and memory
     * Prevents any further ad activity or memory leaks after navigation
     */
    private void performCompleteAdCleanup() {
        Log.d(TAG, "🧹 STARTING COMPREHENSIVE AD CLEANUP: Clearing all ad resources");

        try {
            // 1. Clear interstitial ad callback to prevent further callbacks
            if (mInterstitialAd != null && adContentCallback != null) {
                Log.d(TAG, "🗑️ Clearing ad callback to prevent further ad events");
                mInterstitialAd.setFullScreenContentCallback(null);
            }

            // 2. Clear interstitial ad reference
            if (mInterstitialAd != null) {
                Log.d(TAG, "🗑️ Clearing interstitial ad reference");
                mInterstitialAd = null;
            }

            // 3. Clear callback reference
            if (adContentCallback != null) {
                Log.d(TAG, "🗑️ Clearing ad content callback reference");
                adContentCallback = null;
            }

            // 4. Reset all ad-related flags
            Log.d(TAG, "🗑️ Resetting all ad-related state flags");
            adLoaded = false;
            adShown = false;
            adFullyViewed = false;
            adLoadingCompleted = true; // Mark as completed to prevent further loading

            // 5. Restore status bar if needed
            Log.d(TAG, "🎨 Ensuring status bar is restored during cleanup");
            restoreStatusBar();

            // 6. Force garbage collection to free ad-related memory
            Log.d(TAG, "🗑️ Forcing garbage collection to free ad memory");
            System.gc();

            Log.d(TAG, "✅ COMPREHENSIVE AD CLEANUP COMPLETE: All ad resources cleared");
            Log.d(TAG, "🚫 NO FURTHER AD ACTIVITY: All ad callbacks and listeners removed");
            Log.d(TAG, "💾 MEMORY FREED: Ad-related memory released for optimal performance");

        } catch (Exception e) {
            Log.e(TAG, "❌ ERROR DURING AD CLEANUP: " + e.getMessage(), e);
            // Force cleanup even if errors occur
            mInterstitialAd = null;
            adContentCallback = null;
            System.gc();
        }
    }

    /**
     * ✅ Legacy Ad Cleanup Method - Redirects to Comprehensive Cleanup
     * Maintained for backward compatibility
     */
    private void clearAllAdsFromMemory() {
        Log.d(TAG, "🔄 REDIRECTING TO COMPREHENSIVE CLEANUP: Using enhanced cleanup method");
        performCompleteAdCleanup();
    }

    /**
     * ✅ Navigate to Permission Screen - SAFE NAVIGATION WITH CLEANUP
     * Ensures complete ad cleanup before navigation and prevents duplicate navigation
     */
    private void navigateToPermissionScreen() {
        // Prevent multiple navigation attempts
        if (navigationInProgress) {
            Log.d(TAG, "🚫 NAVIGATION ALREADY IN PROGRESS: Preventing duplicate navigation");
            return;
        }

        navigationInProgress = true;

        Log.d(TAG, "🔐 SMART NAVIGATION: Determining navigation based on Android version and permissions");
        Log.d(TAG, "✅ FLOW: Splash → Update Check → Ad Display → Smart Permission Check → Destination");

        try {
            // Final cleanup before navigation
            Log.d(TAG, "🧹 FINAL CLEANUP: Ensuring all resources are cleared before navigation");
            clearAllHandlers(); // Clear any pending handlers

            // Smart navigation based on Android version
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
                // Android 6-9: No permission screen needed, go directly to MainActivity
                Log.d(TAG, "📱 Android 6-9 (API " + Build.VERSION.SDK_INT + "): Skipping permission screen, going directly to MainActivity");
                navigateDirectlyToMainActivity();
            } else {
                // Android 10+: Check if SAF permissions already exist
                Log.d(TAG, "📱 Android 10+ (API " + Build.VERSION.SDK_INT + "): Checking existing SAF permissions");

                boolean hasExistingPermissions = checkExistingSAFPermissions();

                if (hasExistingPermissions) {
                    Log.d(TAG, "✅ SAF permissions already granted - skipping permission screen");
                    navigateDirectlyToMainActivity();
                } else {
                    Log.d(TAG, "❌ SAF permissions not granted - showing permission screen");
                    navigateToSAFPermissionScreen();
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ ERROR DURING SMART NAVIGATION: " + e.getMessage(), e);
            // Fallback: show permission screen for safety
            navigateToSAFPermissionScreen();
        }
    }

    /**
     * ✅ Check Existing SAF Permissions
     * Returns true if SAF permissions are already granted for Android 10+
     */
    private boolean checkExistingSAFPermissions() {
        try {
            boolean hasAccess = SAFUtils.hasWhatsAppStatusAccess(this);
            Log.d(TAG, "🔍 SAF Permission Check Result: " + (hasAccess ? "✅ GRANTED" : "❌ NOT GRANTED"));
            return hasAccess;
        } catch (Exception e) {
            Log.e(TAG, "❌ Error checking SAF permissions: " + e.getMessage(), e);
            return false; // Default to false for safety
        }
    }

    /**
     * ✅ Navigate Directly to MainActivity
     * Skips permission screen entirely
     */
    private void navigateDirectlyToMainActivity() {
        try {
            Log.d(TAG, "🏠 DIRECT NAVIGATION: Going directly to MainActivity");
            Intent intent = new Intent(SplashActivity.this, MainActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        } catch (Exception e) {
            Log.e(TAG, "❌ Error navigating to MainActivity: " + e.getMessage(), e);
            // Fallback to permission screen
            navigateToSAFPermissionScreen();
        }
    }

    /**
     * ✅ Navigate to SAF Permission Screen
     * Shows permission screen for Android 10+ when permissions not granted
     */
    private void navigateToSAFPermissionScreen() {
        try {
            Log.d(TAG, "🔐 SAF NAVIGATION: Going to SafPermissionActivity");
            Intent intent = new Intent(SplashActivity.this, SafPermissionActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        } catch (Exception e) {
            Log.e(TAG, "❌ Error navigating to SafPermissionActivity: " + e.getMessage(), e);
            // Force finish to prevent hanging
            finish();
        }
    }

    /**
     * ✅ Legacy Navigation Method - Redirects to Smart Navigation
     * Maintained for backward compatibility
     */
    private void navigateToMainActivity() {
        Log.d(TAG, "🔄 LEGACY REDIRECT: Using smart navigation method");
        navigateToPermissionScreen();
    }


    /**
     * ✅ Clear Timeout Handler - Prevents unnecessary timeout execution
     * Called when both conditions are met to clean up pending timeout
     */
    private void clearTimeoutHandler() {
        if (mainHandler != null && timeoutRunnable != null) {
            Log.d(TAG, "🧹 TIMEOUT CLEARED: Removing timeout handler to prevent unnecessary execution");
            mainHandler.removeCallbacks(timeoutRunnable);
        }
    }

    /**
     * ✅ Set Status Bar to Black for Ad Display
     * Makes status bar black during interstitial ad display
     */
    private void setStatusBarBlack() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                Window window = getWindow();
                if (window != null) {
                    Log.d(TAG, "🎨 SETTING STATUS BAR BLACK: For ad display");

                    // Save original state
                    originalStatusBarState = true;

                    // Set status bar to black
                    window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                    window.setStatusBarColor(android.graphics.Color.BLACK);

                    // Set status bar content to light (white icons on black background)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        View decorView = window.getDecorView();
                        int flags = decorView.getSystemUiVisibility();
                        // Remove light status bar flag to make icons white
                        flags &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
                        decorView.setSystemUiVisibility(flags);
                    }

                    Log.d(TAG, "✅ STATUS BAR BLACK: Set successfully for ad display");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ ERROR SETTING STATUS BAR BLACK: " + e.getMessage(), e);
        }
    }

    /**
     * ✅ Restore Original Status Bar
     * Restores status bar to original state after ad
     */
    private void restoreStatusBar() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && originalStatusBarState) {
                Window window = getWindow();
                if (window != null) {
                    Log.d(TAG, "🎨 RESTORING STATUS BAR: Back to original state");

                    // Restore to app's theme color
                    window.setStatusBarColor(getResources().getColor(R.color.statusBarColor, getTheme()));

                    // Restore status bar content based on theme
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        View decorView = window.getDecorView();
                        int flags = decorView.getSystemUiVisibility();
                        // Add light status bar flag for dark icons on light background
                        flags |= View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
                        decorView.setSystemUiVisibility(flags);
                    }

                    originalStatusBarState = false;
                    Log.d(TAG, "✅ STATUS BAR RESTORED: Back to original state");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ ERROR RESTORING STATUS BAR: " + e.getMessage(), e);
        }
    }

    /**
     * ✅ Clear All Handlers - Complete cleanup of all pending handlers
     * Called in onDestroy to prevent memory leaks
     */
    private void clearAllHandlers() {
        if (mainHandler != null) {
            Log.d(TAG, "🧹 CLEANUP: Removing all pending handlers");
            if (splashTimerRunnable != null) {
                mainHandler.removeCallbacks(splashTimerRunnable);
            }
            if (timeoutRunnable != null) {
                mainHandler.removeCallbacks(timeoutRunnable);
            }
        }
    }

    /**
     * ✅ STEP 1: Start In-App Update Check (Parallel with Ad Loading)
     *
     * Splash Screen Flow Implementation:
     * 🟡 1. Display splash screen (3-5 seconds)
     * 🔄 2. Simultaneously start: Update Check + Ad Loading
     * 🟠 3. Update Check Logic:
     *     ✅ If available → Show update screen and STOP flow
     *     ❌ If not available/fails → Continue waiting for ad result
     */
    private void startInAppUpdateCheck() {
        Log.d(TAG, "� SPLASH FLOW STEP 1: Starting in-app update check (parallel with ad loading)");

        if (updateHelper != null && !isFinishing() && !isDestroyed()) {
            try {
                updateHelper.startImmediateUpdateWithCallback(new InAppUpdateHelper.UpdateCallback() {
                    @Override
                    public void onUpdateAvailable() {
                        Log.d(TAG, "🚨 SPLASH FLOW: Update available - STOPPING rest of flow");
                        Log.d(TAG, "🛑 SPLASH FLOW: Do not proceed to ad or next screen");
                        updateAvailable = true;
                        updateCheckCompleted = true;
                        // STOP HERE - Update UI is shown, don't proceed to next screen
                        // Do NOT call checkConditionsAndProceed()
                    }

                    @Override
                    public void onNoUpdateNeeded() {
                        Log.d(TAG, "✅ SPLASH FLOW: No update required - continue waiting for ad result");
                        updateAvailable = false;
                        updateCheckCompleted = true;
                        checkBothConditionsAndProceed();
                    }

                    @Override
                    public void onUpdateCheckFailed() {
                        Log.d(TAG, "❌ SPLASH FLOW: Update check failed - continue waiting for ad result (fail-safe)");
                        updateAvailable = false;
                        updateCheckCompleted = true;
                        checkBothConditionsAndProceed();
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "❌ SPLASH FLOW: Exception during update check - fail-safe behavior", e);
                // Fail-safe: Continue flow even if update check crashes
                updateAvailable = false;
                updateCheckCompleted = true;
                checkBothConditionsAndProceed();
            }
        } else {
            Log.w(TAG, "⚠️ SPLASH FLOW: Cannot start update check - fail-safe behavior");
            // Fail-safe: Continue flow when update check can't start
            updateAvailable = false;
            updateCheckCompleted = true;
            checkBothConditionsAndProceed();
        }
    }

    /**
     * ✅ STEP 4: Check Both Conditions and Proceed to Next Screen
     *
     * Splash Screen Flow Implementation:
     * 🟢 4. Proceed to Next Screen when BOTH conditions met:
     *     🚫 No update required (or update check failed)
     *     ✅ Ad is either loaded or failed
     *
     * 🔁 Fail-Safe Behavior:
     *     ✅ If either ad load or update check fails → Still move to next screen
     *     ✅ Nothing should block the flow except available updates
     */
    private void checkBothConditionsAndProceed() {
        Log.d(TAG, "🟢 SPLASH FLOW STEP 4: Checking both conditions to proceed to next screen");
        Log.d(TAG, "   🔄 Update check completed: " + updateCheckCompleted);
        Log.d(TAG, "   🚨 Update available: " + updateAvailable);
        Log.d(TAG, "   📺 Ad loading completed: " + adLoadingCompleted);
        Log.d(TAG, "   ⏰ Splash timer finished: " + splashTimerFinished);

        // CRITICAL BLOCKING CONDITION: Update available
        if (updateAvailable) {
            Log.d(TAG, "🛑 SPLASH FLOW: Update available - STOPPED at update screen");
            Log.d(TAG, "🛑 SPLASH FLOW: Do not proceed to next screen");
            return; // STOP - Update UI is active, don't proceed
        }

        // PROCEED CONDITIONS: Both async tasks complete
        boolean updateConditionMet = updateCheckCompleted && !updateAvailable;
        boolean adConditionMet = adLoadingCompleted; // Either loaded or failed

        if (updateConditionMet && adConditionMet) {
            // Ensure minimum splash time for professional appearance
            if (splashTimerFinished) {
                Log.d(TAG, "✅ SPLASH FLOW: Both conditions met - PROCEEDING to next screen");
                Log.d(TAG, "   ✅ No update required");
                Log.d(TAG, "   ✅ Ad loading complete (success or failure)");
                Log.d(TAG, "   ✅ Minimum splash time elapsed");
                showInterstitialAdOrProceed();
            } else {
                Log.d(TAG, "⏳ SPLASH FLOW: Conditions met, waiting for minimum splash time");
                // Timer will call this method again when done
            }
        } else {
            Log.d(TAG, "⏳ SPLASH FLOW: Waiting for conditions to complete:");
            if (!updateConditionMet) {
                Log.d(TAG, "   ⏳ Update check still in progress...");
            }
            if (!adConditionMet) {
                Log.d(TAG, "   ⏳ Ad loading still in progress...");
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "🔄 ON RESUME: SplashActivity resumed - checking for dismissed update UI");

        // ✅ CRITICAL: Detect if user dismissed update UI (X, back, task swipe)
        // Google does not notify when user closes update UI - we must check in onResume
        if (updateHelper != null) {
            updateHelper.resumeUpdateIfInProgress();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // Handle in-app update result
        if (updateHelper != null) {
            updateHelper.handleActivityResult(requestCode, resultCode);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        Log.d(TAG, "🧹 ACTIVITY DESTROY: Performing comprehensive cleanup of all resources");

        try {
            // 1. Clear all handlers to prevent memory leaks
            Log.d(TAG, "🧹 Clearing all handlers and runnables");
            clearAllHandlers();

            // 2. Restore status bar
            Log.d(TAG, "🎨 Restoring status bar during destroy");
            restoreStatusBar();

            // 3. Perform complete ad cleanup
            Log.d(TAG, "🧹 Performing complete ad resource cleanup");
            performCompleteAdCleanup();

            // 4. Reset navigation flag
            navigationInProgress = false;

            Log.d(TAG, "✅ DESTROY CLEANUP COMPLETE: All resources, ads, and callbacks cleaned up");
            Log.d(TAG, "🚫 NO MEMORY LEAKS: All references cleared and callbacks removed");

        } catch (Exception e) {
            Log.e(TAG, "❌ ERROR DURING DESTROY CLEANUP: " + e.getMessage(), e);
            // Force cleanup even if errors occur
            try {
                mInterstitialAd = null;
                adContentCallback = null;
                mainHandler = null;
                System.gc();
            } catch (Exception cleanupError) {
                Log.e(TAG, "❌ FORCE CLEANUP ERROR: " + cleanupError.getMessage());
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.d(TAG, "⏸️ ACTIVITY PAUSED: User left the app during splash/ad");

        // If ad is currently showing and user leaves, ensure cleanup when they return
        if (adShown && !adFullyViewed && !navigationInProgress) {
            Log.d(TAG, "⚠️ AD INTERRUPTED: User left during ad display");
            Log.d(TAG, "🧹 PREPARING CLEANUP: Will clean up ad resources if user doesn't return");
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.d(TAG, "⏹️ ACTIVITY STOPPED: App is no longer visible");

        // If user has left the app during ad display, clean up resources
        if (adShown && !adFullyViewed && !navigationInProgress) {
            Log.d(TAG, "🧹 CLEANING UP INTERRUPTED AD: User left during ad display");
            performCompleteAdCleanup();
        }
    }

    @Override
    public void onBackPressed() {
        // Disable back button during splash screen and ad display
        Log.d(TAG, "🚫 BACK BUTTON DISABLED: User tried to go back during splash/ad");
        Log.d(TAG, "⏳ Please wait for the process to complete...");
        Log.d(TAG, "🎯 MANDATORY VIEWING: Ad must be viewed completely before proceeding");
        // Do nothing - prevent user from interrupting the flow
    }
}

package com.ks.app.service.statussaver.ui.splash;

import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.animation.AlphaAnimation;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.ui.base.BaseActivity;
import com.ks.app.service.statussaver.utils.NavigationHelper;
import com.ks.app.service.statussaver.utils.BackgroundAdManager;
import com.ks.app.service.statussaver.utils.AdHelper;

/**
 * ✅ Optimized Splash Activity - Clean Architecture
 * Handles 4-second splash with background ad loading
 * Uses NavigationHelper for smart permission-based navigation
 * No in-app update logic (handled globally by BaseActivity)
 */
public class SplashActivity extends BaseActivity {

    private static final String TAG = "SplashActivity";
    private static final int SPLASH_DURATION = 4000; // 4 seconds - minimum splash time

    // Ad management with on-demand loading
    private boolean adLoadingCompleted = false;
    private boolean adShown = false;

    // Timer management
    private boolean splashTimerFinished = false;
    private boolean navigationInProgress = false;

    // Handler for timing
    private Handler mainHandler;
    private Runnable splashTimerRunnable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        Log.d(TAG, "🚀 OPTIMIZED SPLASH: Starting 4-second splash with background ad loading");
        Log.d(TAG, "🔍 UPDATE HANDLING: Managed globally by BaseActivity");

        // Set status bar color to match splash background
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setStatusBarColor(getResources().getColor(R.color.splash_background, getTheme()));
        }

        // Hide the action bar for full screen splash
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        // Initialize views and animations
        initializeViewsAndAnimations();

        // Initialize handler
        mainHandler = new Handler(Looper.getMainLooper());

        // Start the optimized splash flow with BackgroundAdManager
        startOptimizedSplashFlow();
    }

    /**
     * ✅ Initialize Views and Animations
     */
    private void initializeViewsAndAnimations() {
        // Initialize views
        ImageView splashIcon = findViewById(R.id.splash_icon);
        TextView appName = findViewById(R.id.splash_app_name);
        ProgressBar loadingIndicator = findViewById(R.id.splash_loading_indicator);

        // Create fade-in animation
        AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
        fadeIn.setDuration(1000); // 1 second fade-in
        fadeIn.setStartOffset(200); // Start after 200ms

        // Apply animations
        splashIcon.startAnimation(fadeIn);
        appName.startAnimation(fadeIn);
        loadingIndicator.startAnimation(fadeIn);

        Log.d(TAG, "🎭 ANIMATIONS: Splash animations started");
    }

    /**
     * ✅ Start Optimized Splash Flow
     * Handles 4-second timer and efficient background ad loading
     */
    private void startOptimizedSplashFlow() {
        Log.d(TAG, "⏰ SPLASH TIMER: Starting 4-second countdown");

        // Start on-demand ad loading
        startOnDemandAdLoading();

        // Create and start 4-second timer
        splashTimerRunnable = new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "⏰ SPLASH TIMER: 4 seconds completed");
                splashTimerFinished = true;
                checkAdStatusAndProceed();
            }
        };

        // Start the 4-second timer
        mainHandler.postDelayed(splashTimerRunnable, SPLASH_DURATION);
    }

    /**
     * ✅ Check Ad Status and Proceed
     * Called after 4-second timer completes
     */
    private void checkAdStatusAndProceed() {
        if (navigationInProgress) {
            Log.d(TAG, "🚫 NAVIGATION IN PROGRESS: Skipping duplicate navigation");
            return;
        }

        Log.d(TAG, "📊 AD STATUS CHECK: Timer finished, checking ad status");
        Log.d(TAG, "   🔄 Ad Loading Completed: " + adLoadingCompleted);

        if (adLoadingCompleted) {
            // Ad loading completed (success or failure) - proceed
            Log.d(TAG, "✅ AD COMPLETED: Proceeding to navigation");
            proceedToNavigation();
        } else {
            // Ad still loading - wait for it
            Log.d(TAG, "⏳ AD STILL LOADING: Waiting for ad to complete");
            // The ad callback will handle navigation when ready
        }
    }

    /**
     * ✅ Start On-Demand Ad Loading
     * Loads ad when needed without caching
     */
    private void startOnDemandAdLoading() {
        Log.d(TAG, "🚀 ON-DEMAND AD LOADING: Starting reliable ad load");

        // Use BackgroundAdManager for on-demand loading and display
        BackgroundAdManager.getInstance().loadAndShowInterstitialAd(this,
            new BackgroundAdManager.AdShowCallback() {
                @Override
                public void onAdShown() {
                    Log.d(TAG, "📺 AD SHOWN: Interstitial displayed successfully");
                    adShown = true;
                }

                @Override
                public void onAdDismissed() {
                    Log.d(TAG, "✅ AD DISMISSED: User closed ad, proceeding to navigation");
                    adLoadingCompleted = true;

                    // If timer already finished, proceed immediately
                    if (splashTimerFinished) {
                        proceedToNavigation();
                    }
                }

                @Override
                public void onAdFailed(String error) {
                    Log.w(TAG, "❌ AD FAILED: " + error + ", proceeding without ad");
                    adLoadingCompleted = true;

                    // If timer already finished, proceed immediately
                    if (splashTimerFinished) {
                        proceedToNavigation();
                    }
                }
            }
        );
    }



    /**
     * ✅ Proceed to Navigation
     * Uses NavigationHelper for smart permission-based navigation
     */
    private void proceedToNavigation() {
        if (navigationInProgress && !adShown) {
            Log.d(TAG, "🚫 NAVIGATION ALREADY IN PROGRESS: Preventing duplicate navigation");
            return;
        }

        navigationInProgress = true;
        Log.d(TAG, "🔍 SMART NAVIGATION: Using NavigationHelper for permission-based routing");

        // Clean up resources
        cleanupResources();

        // Use NavigationHelper for smart navigation
        NavigationHelper.checkPermissionsAndNavigate(this);
    }

    /**
     * ✅ Clean Up Resources
     * Cleans up handlers and ad resources before navigation
     */
    private void cleanupResources() {
        Log.d(TAG, "🧹 CLEANUP: Cleaning up splash screen resources");

        // Clean up handler
        if (mainHandler != null && splashTimerRunnable != null) {
            mainHandler.removeCallbacks(splashTimerRunnable);
        }

        Log.d(TAG, "✅ CLEANUP COMPLETE: All resources cleaned up");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "💀 ACTIVITY DESTROYED: Cleaning up resources");
        cleanupResources();
    }

    @Override
    public void onBackPressed() {
        // Disable back button during splash screen
        Log.d(TAG, "🚫 BACK BUTTON DISABLED: Please wait for splash to complete");
        // Do nothing - prevent user from interrupting the flow
    }
}

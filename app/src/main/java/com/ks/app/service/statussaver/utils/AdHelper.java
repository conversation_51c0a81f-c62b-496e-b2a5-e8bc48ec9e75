package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.util.Log;

/**
 * ✅ Ad Helper - Simple Interface for Ad Operations
 * 
 * Provides easy-to-use methods for showing ads throughout the app
 * Uses BackgroundAdManager for efficient ad loading and caching
 */
public class AdHelper {
    
    private static final String TAG = "AdHelper";
    
    /**
     * ✅ Show Interstitial Ad with Callback
     * 
     * @param activity Current activity
     * @param onComplete Callback when ad is dismissed or failed
     */
    public static void showInterstitialAd(Activity activity, Runnable onComplete) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            Log.w(TAG, "⚠️ INVALID ACTIVITY: Cannot show ad");
            if (onComplete != null) {
                onComplete.run();
            }
            return;
        }
        
        Log.d(TAG, "📺 SHOWING AD: Requesting interstitial ad display");
        
        // Use BackgroundAdManager for efficient ad display
        BackgroundAdManager.getInstance().showInterstitialAd(activity, onComplete);
    }
    
    /**
     * ✅ Show Interstitial Ad (Simple Version)
     * 
     * @param activity Current activity
     */
    public static void showInterstitialAd(Activity activity) {
        showInterstitialAd(activity, null);
    }
    
    /**
     * ✅ Check if Ad is Ready
     * 
     * @return true if ad is loaded and ready to show
     */
    public static boolean isAdReady() {
        boolean ready = BackgroundAdManager.getInstance().isAdReady();
        Log.d(TAG, "🔍 AD READY CHECK: " + ready);
        return ready;
    }
    
    /**
     * ✅ Preload Next Ad
     * Call this after showing an ad to preload the next one
     */
    public static void preloadNextAd() {
        Log.d(TAG, "🔄 PRELOADING: Starting background ad preload");
        BackgroundAdManager.getInstance().startBackgroundAdPreloading();
    }
    
    /**
     * ✅ Show Ad with Navigation
     * Shows ad and then navigates using NavigationHelper
     * 
     * @param activity Current activity
     */
    public static void showAdAndNavigate(Activity activity) {
        showInterstitialAd(activity, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "✅ AD COMPLETED: Proceeding to smart navigation");
                NavigationHelper.checkPermissionsAndNavigate(activity);
            }
        });
    }
    
    /**
     * ✅ Show Ad with Custom Navigation
     * Shows ad and then executes custom navigation logic
     * 
     * @param activity Current activity
     * @param navigationAction Custom navigation to execute after ad
     */
    public static void showAdWithCustomNavigation(Activity activity, Runnable navigationAction) {
        showInterstitialAd(activity, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "✅ AD COMPLETED: Executing custom navigation");
                if (navigationAction != null) {
                    navigationAction.run();
                }
            }
        });
    }
}

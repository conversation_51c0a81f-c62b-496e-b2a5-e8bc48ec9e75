package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.util.Log;

/**
 * ✅ Ad Helper - Simple Interface for Ad Operations
 *
 * Provides easy-to-use methods for showing ads throughout the app
 * Uses BackgroundAdManager for reliable on-demand ad loading
 */
public class AdHelper {

    private static final String TAG = "AdHelper";

    /**
     * ✅ Show Interstitial Ad with Callback
     *
     * @param activity Current activity
     * @param onComplete Callback when ad is dismissed or failed
     */
    public static void showInterstitialAd(Activity activity, Runnable onComplete) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            Log.w(TAG, "⚠️ INVALID ACTIVITY: Cannot show ad");
            if (onComplete != null) {
                onComplete.run();
            }
            return;
        }

        Log.d(TAG, "📺 SHOWING AD: Loading and displaying ad on-demand");

        // Use BackgroundAdManager for on-demand loading and display
        BackgroundAdManager.getInstance().loadAndShowInterstitialAd(activity,
            new BackgroundAdManager.AdShowCallback() {
                @Override
                public void onAdShown() {
                    Log.d(TAG, "📺 AD SHOWN: Interstitial ad displayed successfully");
                }

                @Override
                public void onAdDismissed() {
                    Log.d(TAG, "✅ AD DISMISSED: User closed ad");
                    if (onComplete != null) {
                        onComplete.run();
                    }
                }

                @Override
                public void onAdFailed(String error) {
                    Log.w(TAG, "❌ AD FAILED: " + error);
                    if (onComplete != null) {
                        onComplete.run();
                    }
                }
            });
    }
    
    /**
     * ✅ Show Interstitial Ad (Simple Version)
     *
     * @param activity Current activity
     */
    public static void showInterstitialAd(Activity activity) {
        showInterstitialAd(activity, null);
    }

    /**
     * ✅ Check if Ad is Currently Loading
     *
     * @return true if ad is currently being loaded
     */
    public static boolean isAdLoading() {
        boolean loading = BackgroundAdManager.getInstance().isLoading();
        Log.d(TAG, "🔍 AD LOADING CHECK: " + loading);
        return loading;
    }
    
    /**
     * ✅ Show Ad with Navigation
     * Shows ad and then navigates using NavigationHelper
     * 
     * @param activity Current activity
     */
    public static void showAdAndNavigate(Activity activity) {
        showInterstitialAd(activity, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "✅ AD COMPLETED: Proceeding to smart navigation");
                NavigationHelper.checkPermissionsAndNavigate(activity);
            }
        });
    }
    
    /**
     * ✅ Show Ad with Custom Navigation
     * Shows ad and then executes custom navigation logic
     * 
     * @param activity Current activity
     * @param navigationAction Custom navigation to execute after ad
     */
    public static void showAdWithCustomNavigation(Activity activity, Runnable navigationAction) {
        showInterstitialAd(activity, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "✅ AD COMPLETED: Executing custom navigation");
                if (navigationAction != null) {
                    navigationAction.run();
                }
            }
        });
    }
}

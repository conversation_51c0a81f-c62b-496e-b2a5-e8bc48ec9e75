package com.ks.app.service.statussaver.utils;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import com.ks.app.service.statussaver.ui.ads.InterstitialAdActivity;

/**
 * Utility class for managing ads throughout the app
 */
public class AdUtils {
    
    private static final String TAG = "AdUtils";
    
    /**
     * Launch InterstitialAdActivity with smooth transitions
     * 
     * @param context Current context
     * @param nextActivity Target activity class name
     * @param extras Optional extras to pass to target activity
     */
    public static void showInterstitialAd(Context context, String nextActivity, Bundle extras) {
        try {
            Log.d(TAG, "🎬 LAUNCHING AD SCREEN: Target -> " + nextActivity);
            
            Intent intent = new Intent(context, InterstitialAdActivity.class);
            intent.putExtra("next_activity", nextActivity);
            
            if (extras != null) {
                intent.putExtra("next_activity_extras", extras);
            }
            
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            context.startActivity(intent);
            
            Log.d(TAG, "✅ AD SCREEN LAUNCHED: Successfully started InterstitialAdActivity");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ AD LAUNCH ERROR: " + e.getMessage(), e);
            // Fallback - navigate directly to target activity
            navigateDirectly(context, nextActivity, extras);
        }
    }
    
    /**
     * Launch InterstitialAdActivity to MainActivity (most common use case)
     * 
     * @param context Current context
     */
    public static void showInterstitialAdToMain(Context context) {
        showInterstitialAd(context, "MainActivity", null);
    }
    
    /**
     * Launch InterstitialAdActivity to SafPermissionActivity
     * 
     * @param context Current context
     */
    public static void showInterstitialAdToPermission(Context context) {
        showInterstitialAd(context, "SafPermissionActivity", null);
    }
    
    /**
     * Fallback method to navigate directly if ad fails
     * 
     * @param context Current context
     * @param nextActivity Target activity class name
     * @param extras Optional extras
     */
    private static void navigateDirectly(Context context, String nextActivity, Bundle extras) {
        try {
            Log.d(TAG, "🔄 DIRECT NAVIGATION: Fallback to " + nextActivity);
            
            Intent intent;
            
            switch (nextActivity) {
                case "MainActivity":
                    intent = new Intent(context, com.ks.app.service.statussaver.ui.main.MainActivity.class);
                    break;
                case "SafPermissionActivity":
                    intent = new Intent(context, com.ks.app.service.statussaver.ui.main.SafPermissionActivity.class);
                    break;
                default:
                    intent = new Intent(context, com.ks.app.service.statussaver.ui.main.MainActivity.class);
                    break;
            }
            
            if (extras != null) {
                intent.putExtras(extras);
            }
            
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            context.startActivity(intent);
            
            Log.d(TAG, "✅ DIRECT NAVIGATION SUCCESS: Reached " + nextActivity);
            
        } catch (Exception e) {
            Log.e(TAG, "❌ DIRECT NAVIGATION ERROR: " + e.getMessage(), e);
        }
    }
    
    /**
     * Check if ads should be shown (can be used for premium users, etc.)
     * 
     * @param context Current context
     * @return true if ads should be shown
     */
    public static boolean shouldShowAds(Context context) {
        // For now, always show ads
        // You can add logic here for premium users, etc.
        return true;
    }
    
    /**
     * Get the interstitial ad unit ID
     * 
     * @return Ad unit ID string
     */
    public static String getInterstitialAdUnitId() {
        return "ca-app-pub-7557152164205920/6418571631";
    }
}

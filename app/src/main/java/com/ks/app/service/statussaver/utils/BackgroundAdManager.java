package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;

/**
 * ✅ Background Ad Manager - Efficient Interstitial Ad Loading
 * 
 * Features:
 * - Preloads ads in background during app initialization
 * - Caches loaded ads for instant display
 * - Automatic retry mechanism for failed loads
 * - Memory-efficient ad management
 * - Production-ready with your ad units
 */
public class BackgroundAdManager {
    
    private static final String TAG = "BackgroundAdManager";
    private static BackgroundAdManager instance;
    
    // Your production ad units
    private static final String APP_ID = "ca-app-pub-7557152164205920~6417975169";
    private static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-7557152164205920/6418571631";
    
    // Ad management
    private InterstitialAd cachedInterstitialAd;
    private boolean isAdLoading = false;
    private boolean isAdLoaded = false;
    private long adLoadStartTime = 0;
    
    // Context management
    private Context applicationContext;
    private Activity currentActivity;
    
    // Retry mechanism
    private int retryAttempts = 0;
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 2000; // 2 seconds
    
    // Callbacks
    private AdLoadCallback adLoadCallback;
    private Handler mainHandler;
    
    /**
     * ✅ Ad Load Callback Interface
     */
    public interface AdLoadCallback {
        void onAdLoaded();
        void onAdFailedToLoad(String error);
    }
    
    /**
     * ✅ Singleton Instance
     */
    public static synchronized BackgroundAdManager getInstance() {
        if (instance == null) {
            instance = new BackgroundAdManager();
        }
        return instance;
    }
    
    private BackgroundAdManager() {
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * ✅ Initialize Background Ad Manager
     * Call this from Application.onCreate() for early initialization
     */
    public void initialize(Context context) {
        this.applicationContext = context.getApplicationContext();
        
        Log.d(TAG, "🚀 BACKGROUND AD MANAGER: Initializing");
        Log.d(TAG, "📱 App ID: " + APP_ID);
        Log.d(TAG, "📺 Interstitial Unit: " + INTERSTITIAL_AD_UNIT_ID);
        
        // Initialize AdMob SDK if not already done
        MobileAds.initialize(applicationContext, initializationStatus -> {
            Log.d(TAG, "✅ ADMOB SDK: Initialized for background ad loading");
            
            // Start preloading ads immediately
            startBackgroundAdPreloading();
        });
    }
    
    /**
     * ✅ Start Background Ad Preloading
     * Begins loading ads in background for instant availability
     */
    public void startBackgroundAdPreloading() {
        if (isAdLoading || isAdLoaded) {
            Log.d(TAG, "🔄 AD PRELOADING: Already loading or loaded, skipping");
            return;
        }
        
        Log.d(TAG, "🚀 BACKGROUND PRELOADING: Starting interstitial ad load");
        loadInterstitialAdInBackground(null);
    }
    
    /**
     * ✅ Load Interstitial Ad with Callback
     * For specific loading with callback (e.g., splash screen)
     */
    public void loadInterstitialAdWithCallback(AdLoadCallback callback) {
        this.adLoadCallback = callback;
        
        if (isAdLoaded && cachedInterstitialAd != null) {
            Log.d(TAG, "✅ CACHED AD: Using already loaded ad");
            if (callback != null) {
                callback.onAdLoaded();
            }
            return;
        }
        
        loadInterstitialAdInBackground(callback);
    }
    
    /**
     * ✅ Load Interstitial Ad in Background
     * Core ad loading method with retry mechanism
     */
    private void loadInterstitialAdInBackground(AdLoadCallback callback) {
        if (isAdLoading) {
            Log.d(TAG, "🔄 AD LOADING: Already in progress, skipping");
            return;
        }
        
        isAdLoading = true;
        adLoadStartTime = System.currentTimeMillis();
        retryAttempts++;
        
        Log.d(TAG, "🚀 LOADING AD: Attempt " + retryAttempts + "/" + MAX_RETRY_ATTEMPTS);
        Log.d(TAG, "📱 Ad Unit: " + INTERSTITIAL_AD_UNIT_ID);
        
        // Create optimized ad request
        AdRequest adRequest = new AdRequest.Builder()
            .build();
        
        // Load ad with callbacks
        InterstitialAd.load(applicationContext, INTERSTITIAL_AD_UNIT_ID, adRequest,
            new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.d(TAG, "✅ AD LOADED: Successfully loaded in " + loadTime + "ms");
                    Log.d(TAG, "🎯 ATTEMPT: " + retryAttempts + "/" + MAX_RETRY_ATTEMPTS);
                    
                    // Cache the ad
                    cachedInterstitialAd = interstitialAd;
                    isAdLoaded = true;
                    isAdLoading = false;
                    retryAttempts = 0; // Reset retry counter
                    
                    // Notify callback
                    if (callback != null) {
                        callback.onAdLoaded();
                    }
                    if (adLoadCallback != null) {
                        adLoadCallback.onAdLoaded();
                        adLoadCallback = null;
                    }
                    
                    Log.d(TAG, "📦 AD CACHED: Ready for instant display");
                }
                
                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.w(TAG, "❌ AD LOAD FAILED: After " + loadTime + "ms");
                    Log.w(TAG, "📊 Error: " + loadAdError.getMessage());
                    Log.w(TAG, "🔢 Error Code: " + loadAdError.getCode());
                    
                    isAdLoading = false;
                    
                    // Retry mechanism
                    if (retryAttempts < MAX_RETRY_ATTEMPTS) {
                        Log.d(TAG, "🔄 RETRYING: Attempt " + (retryAttempts + 1) + "/" + MAX_RETRY_ATTEMPTS + " in " + RETRY_DELAY_MS + "ms");
                        
                        mainHandler.postDelayed(() -> {
                            loadInterstitialAdInBackground(callback);
                        }, RETRY_DELAY_MS);
                        
                    } else {
                        Log.w(TAG, "❌ MAX RETRIES REACHED: Giving up after " + MAX_RETRY_ATTEMPTS + " attempts");
                        retryAttempts = 0; // Reset for next time
                        
                        // Notify callback of failure
                        String errorMsg = "Failed to load after " + MAX_RETRY_ATTEMPTS + " attempts: " + loadAdError.getMessage();
                        if (callback != null) {
                            callback.onAdFailedToLoad(errorMsg);
                        }
                        if (adLoadCallback != null) {
                            adLoadCallback.onAdFailedToLoad(errorMsg);
                            adLoadCallback = null;
                        }
                    }
                }
            });
    }
    
    /**
     * ✅ Check if Ad is Ready
     * Returns true if ad is loaded and ready to show
     */
    public boolean isAdReady() {
        boolean ready = isAdLoaded && cachedInterstitialAd != null;
        Log.d(TAG, "🔍 AD READY CHECK: " + ready);
        return ready;
    }
    
    /**
     * ✅ Show Interstitial Ad
     * Displays the cached ad if available
     */
    public void showInterstitialAd(Activity activity, Runnable onAdClosed) {
        if (!isAdReady()) {
            Log.w(TAG, "⚠️ NO AD AVAILABLE: Cannot show ad, not loaded");
            if (onAdClosed != null) {
                onAdClosed.run();
            }
            return;
        }
        
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            Log.w(TAG, "⚠️ INVALID ACTIVITY: Cannot show ad");
            if (onAdClosed != null) {
                onAdClosed.run();
            }
            return;
        }
        
        Log.d(TAG, "📺 SHOWING AD: Displaying cached interstitial ad");
        currentActivity = activity;
        
        // Set up ad callbacks
        cachedInterstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "✅ AD DISMISSED: User closed ad");
                cleanupCurrentAd();
                
                // Start preloading next ad
                startBackgroundAdPreloading();
                
                // Notify completion
                if (onAdClosed != null) {
                    onAdClosed.run();
                }
            }
            
            @Override
            public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                Log.w(TAG, "❌ AD SHOW FAILED: " + adError.getMessage());
                cleanupCurrentAd();
                
                // Start preloading next ad
                startBackgroundAdPreloading();
                
                // Notify completion
                if (onAdClosed != null) {
                    onAdClosed.run();
                }
            }
            
            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "📺 AD DISPLAYED: Interstitial ad is now showing");
            }
            
            @Override
            public void onAdClicked() {
                Log.d(TAG, "👆 AD CLICKED: User interacted with ad");
            }
            
            @Override
            public void onAdImpression() {
                Log.d(TAG, "👁️ AD IMPRESSION: Ad impression recorded");
            }
        });
        
        // Show the ad
        cachedInterstitialAd.show(activity);
    }
    
    /**
     * ✅ Cleanup Current Ad
     * Cleans up the currently displayed ad
     */
    private void cleanupCurrentAd() {
        cachedInterstitialAd = null;
        isAdLoaded = false;
        currentActivity = null;
        
        Log.d(TAG, "🧹 AD CLEANUP: Current ad cleaned up");
    }
    
    /**
     * ✅ Cleanup All Resources
     * Call this when app is being destroyed
     */
    public void cleanup() {
        cleanupCurrentAd();
        
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
        
        adLoadCallback = null;
        isAdLoading = false;
        retryAttempts = 0;
        
        Log.d(TAG, "🧹 FULL CLEANUP: All ad resources cleaned up");
    }
}

package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;

/**
 * ✅ Background Ad Manager - Reliable On-Demand Ad Loading
 *
 * Features:
 * - Loads ads on-demand when needed (no caching)
 * - Reliable background loading with proper error handling
 * - Automatic retry mechanism for failed loads
 * - Memory-efficient (no stored ads)
 * - Production-ready with your ad units
 */
public class BackgroundAdManager {

    private static final String TAG = "BackgroundAdManager";
    private static BackgroundAdManager instance;

    // Your production ad units
    private static final String APP_ID = "ca-app-pub-7557152164205920~6417975169";
    private static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-7557152164205920/6418571631";

    // Ad loading state (no caching)
    private boolean isAdLoading = false;
    private long adLoadStartTime = 0;

    // Context management
    private Context applicationContext;
    private boolean sdkInitialized = false;

    // Retry mechanism
    private int retryAttempts = 0;
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 2000; // 2 seconds

    // Callbacks
    private Handler mainHandler;
    
    /**
     * ✅ Ad Load Callback Interface
     */
    public interface AdLoadCallback {
        void onAdLoaded(InterstitialAd ad);
        void onAdFailedToLoad(String error);
    }

    /**
     * ✅ Ad Show Callback Interface
     */
    public interface AdShowCallback {
        void onAdShown();
        void onAdDismissed();
        void onAdFailed(String error);
    }
    
    /**
     * ✅ Singleton Instance
     */
    public static synchronized BackgroundAdManager getInstance() {
        if (instance == null) {
            instance = new BackgroundAdManager();
        }
        return instance;
    }
    
    private BackgroundAdManager() {
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * ✅ Initialize Background Ad Manager
     * Call this from Application.onCreate() for SDK initialization only
     */
    public void initialize(Context context) {
        this.applicationContext = context.getApplicationContext();

        Log.d(TAG, "🚀 BACKGROUND AD MANAGER: Initializing");
        Log.d(TAG, "📱 App ID: " + APP_ID);
        Log.d(TAG, "📺 Interstitial Unit: " + INTERSTITIAL_AD_UNIT_ID);
        Log.d(TAG, "🔄 ON-DEMAND LOADING: No caching, loads when needed");

        // Initialize AdMob SDK only
        MobileAds.initialize(applicationContext, initializationStatus -> {
            sdkInitialized = true;
            Log.d(TAG, "✅ ADMOB SDK: Initialized and ready for on-demand loading");
            Log.d(TAG, "📊 Initialization Status: " + initializationStatus.getAdapterStatusMap());
        });
    }
    
    /**
     * ✅ Load Interstitial Ad On-Demand
     * Loads ad when needed without caching
     */
    public void loadInterstitialAd(AdLoadCallback callback) {
        if (!sdkInitialized) {
            Log.w(TAG, "⚠️ SDK NOT READY: AdMob SDK not initialized yet");
            if (callback != null) {
                callback.onAdFailedToLoad("AdMob SDK not initialized");
            }
            return;
        }

        if (isAdLoading) {
            Log.d(TAG, "🔄 AD LOADING: Already in progress, skipping duplicate request");
            if (callback != null) {
                callback.onAdFailedToLoad("Ad loading already in progress");
            }
            return;
        }

        Log.d(TAG, "🚀 ON-DEMAND LOADING: Starting fresh ad load");
        loadInterstitialAdInBackground(callback);
    }
    
    /**
     * ✅ Load Interstitial Ad in Background
     * Core ad loading method with retry mechanism (no caching)
     */
    private void loadInterstitialAdInBackground(AdLoadCallback callback) {
        isAdLoading = true;
        adLoadStartTime = System.currentTimeMillis();
        retryAttempts++;

        Log.d(TAG, "🚀 LOADING AD: Attempt " + retryAttempts + "/" + MAX_RETRY_ATTEMPTS);
        Log.d(TAG, "📱 Ad Unit: " + INTERSTITIAL_AD_UNIT_ID);
        Log.d(TAG, "🔄 FRESH LOAD: No caching, loading new ad");

        // Create optimized ad request
        AdRequest adRequest = new AdRequest.Builder()
            .build();

        // Load ad with callbacks
        InterstitialAd.load(applicationContext, INTERSTITIAL_AD_UNIT_ID, adRequest,
            new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.d(TAG, "✅ AD LOADED: Successfully loaded in " + loadTime + "ms");
                    Log.d(TAG, "🎯 ATTEMPT: " + retryAttempts + "/" + MAX_RETRY_ATTEMPTS);
                    Log.d(TAG, "📦 FRESH AD: Ready for immediate display (no caching)");

                    // Reset state
                    isAdLoading = false;
                    retryAttempts = 0; // Reset retry counter

                    // Notify callback with the ad
                    if (callback != null) {
                        callback.onAdLoaded(interstitialAd);
                    }
                }
                
                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.w(TAG, "❌ AD LOAD FAILED: After " + loadTime + "ms");
                    Log.w(TAG, "📊 Error: " + loadAdError.getMessage());
                    Log.w(TAG, "🔢 Error Code: " + loadAdError.getCode());
                    Log.w(TAG, "🔍 Error Domain: " + loadAdError.getDomain());

                    // Retry mechanism
                    if (retryAttempts < MAX_RETRY_ATTEMPTS) {
                        Log.d(TAG, "🔄 RETRYING: Attempt " + (retryAttempts + 1) + "/" + MAX_RETRY_ATTEMPTS + " in " + RETRY_DELAY_MS + "ms");

                        mainHandler.postDelayed(() -> {
                            loadInterstitialAdInBackground(callback);
                        }, RETRY_DELAY_MS);

                    } else {
                        Log.w(TAG, "❌ MAX RETRIES REACHED: Giving up after " + MAX_RETRY_ATTEMPTS + " attempts");

                        // Reset state
                        isAdLoading = false;
                        retryAttempts = 0; // Reset for next time

                        // Notify callback of failure
                        String errorMsg = "Failed to load after " + MAX_RETRY_ATTEMPTS + " attempts: " + loadAdError.getMessage();
                        if (callback != null) {
                            callback.onAdFailedToLoad(errorMsg);
                        }
                    }
                }
            });
    }
    
    /**
     * ✅ Load and Show Interstitial Ad
     * Loads ad on-demand and shows it when ready
     */
    public void loadAndShowInterstitialAd(Activity activity, AdShowCallback callback) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            Log.w(TAG, "⚠️ INVALID ACTIVITY: Cannot show ad");
            if (callback != null) {
                callback.onAdFailed("Invalid activity");
            }
            return;
        }

        Log.d(TAG, "🚀 LOAD AND SHOW: Starting on-demand ad load and display");

        // Load ad on-demand
        loadInterstitialAd(new AdLoadCallback() {
            @Override
            public void onAdLoaded(InterstitialAd ad) {
                Log.d(TAG, "✅ AD READY: Showing immediately");
                showLoadedAd(activity, ad, callback);
            }

            @Override
            public void onAdFailedToLoad(String error) {
                Log.w(TAG, "❌ LOAD FAILED: " + error);
                if (callback != null) {
                    callback.onAdFailed("Failed to load: " + error);
                }
            }
        });
    }

    /**
     * ✅ Show Loaded Ad
     * Displays the freshly loaded ad
     */
    private void showLoadedAd(Activity activity, InterstitialAd ad, AdShowCallback callback) {
        Log.d(TAG, "📺 SHOWING AD: Displaying fresh interstitial ad");

        // Set up ad callbacks
        ad.setFullScreenContentCallback(new FullScreenContentCallback() {
            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "✅ AD DISMISSED: User closed ad");
                if (callback != null) {
                    callback.onAdDismissed();
                }
            }

            @Override
            public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                Log.w(TAG, "❌ AD SHOW FAILED: " + adError.getMessage());
                if (callback != null) {
                    callback.onAdFailed("Show failed: " + adError.getMessage());
                }
            }

            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "📺 AD DISPLAYED: Interstitial ad is now showing");
                if (callback != null) {
                    callback.onAdShown();
                }
            }

            @Override
            public void onAdClicked() {
                Log.d(TAG, "👆 AD CLICKED: User interacted with ad");
            }

            @Override
            public void onAdImpression() {
                Log.d(TAG, "👁️ AD IMPRESSION: Ad impression recorded");
            }
        });

        // Show the ad
        ad.show(activity);
    }
    
    /**
     * ✅ Check if Currently Loading
     * Returns true if an ad is currently being loaded
     */
    public boolean isLoading() {
        return isAdLoading;
    }

    /**
     * ✅ Cleanup All Resources
     * Call this when app is being destroyed
     */
    public void cleanup() {
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }

        isAdLoading = false;
        retryAttempts = 0;
        sdkInitialized = false;

        Log.d(TAG, "🧹 FULL CLEANUP: All ad resources cleaned up");
    }
}

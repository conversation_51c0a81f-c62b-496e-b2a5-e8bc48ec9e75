package com.ks.app.service.statussaver.utils;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.widget.Toast;
import com.ks.app.service.statussaver.R;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import android.content.Intent;

public class FileUtils {

    private static final String TAG = "FileUtils";
    private static final String SAVED_FOLDER_NAME = "StatusSaver";

    public static File saveStatus(Context context, Uri sourceUri, String fileName, boolean isVideo) {
        ContentResolver resolver = context.getContentResolver();
        String mimeType = isVideo ? "video/mp4" : "image/jpeg"; // Or png, adjust as needed
        String relativePath = Environment.DIRECTORY_PICTURES + File.separator + SAVED_FOLDER_NAME;
        if (isVideo) {
            relativePath = Environment.DIRECTORY_MOVIES + File.separator + SAVED_FOLDER_NAME;
        }

        ContentValues contentValues = new ContentValues();
        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, fileName);
        contentValues.put(MediaStore.MediaColumns.MIME_TYPE, mimeType);
        contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, relativePath);
        contentValues.put(MediaStore.MediaColumns.IS_PENDING, 1);

        Uri collectionUri;
        if (isVideo) {
            collectionUri = MediaStore.Video.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY);
        } else {
            collectionUri = MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY);
        }

        Uri itemUri = resolver.insert(collectionUri, contentValues);

        if (itemUri != null) {
            try (InputStream in = resolver.openInputStream(sourceUri);
                 OutputStream out = resolver.openOutputStream(itemUri)) {
                if (in != null && out != null) {
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = in.read(buffer)) > 0) {
                        out.write(buffer, 0, len);
                    }
                }
                contentValues.clear();
                contentValues.put(MediaStore.MediaColumns.IS_PENDING, 0);
                resolver.update(itemUri, contentValues, null, null);
                Toast.makeText(context, context.getString(R.string.status_saved_to, relativePath + File.separator + fileName), Toast.LENGTH_LONG).show();
                // Return the File object if needed, though MediaStore handles the path now
                // This part would need adjustment if direct file path is strictly required post-save
                // For now, returning null as MediaStore URI is the primary reference
                 return new File(Environment.getExternalStoragePublicDirectory(relativePath), fileName); // This might not be 100% accurate for all APIs, MediaStore URI is better

            } catch (IOException e) {
                Log.e(TAG, "Error saving status: " + e.getMessage());
                resolver.delete(itemUri, null, null); // Clean up pending item
                Toast.makeText(context, R.string.download_failed, Toast.LENGTH_SHORT).show();
                return null;
            }
        } else {
            Toast.makeText(context, R.string.download_failed, Toast.LENGTH_SHORT).show();
            return null;
        }
    }

    // Fallback for older Android versions if MediaStore method above has issues or for direct path saving.
    public static File saveStatusLegacy(Context context, Uri sourceUri, String fileName, boolean isVideo) {
        File directory;
        if (isVideo) {
            directory = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MOVIES), SAVED_FOLDER_NAME);
        } else {
            directory = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), SAVED_FOLDER_NAME);
        }

        if (!directory.exists()) {
            if (!directory.mkdirs()) {
                Log.e(TAG, "Failed to create directory: " + directory.getAbsolutePath());
                Toast.makeText(context, R.string.download_failed, Toast.LENGTH_SHORT).show();
                return null;
            }
        }

        File destinationFile = new File(directory, fileName);

        try (InputStream in = context.getContentResolver().openInputStream(sourceUri);
             OutputStream out = new FileOutputStream(destinationFile)) {
            if (in == null) {
                Toast.makeText(context, R.string.download_failed, Toast.LENGTH_SHORT).show();
                return null;
            }
            byte[] buf = new byte[1024];
            int len;
            while ((len = in.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
            // Notify MediaScanner to make the file visible in gallery apps immediately
            MediaScannerConnection.scanFile(context, new String[]{destinationFile.getAbsolutePath()}, null,
                    (path, uri) -> {
                        Log.i(TAG, "Scanned " + path + ":");
                        Log.i(TAG, "-> uri=" + uri);
                    });
            Toast.makeText(context, context.getString(R.string.status_saved_to, destinationFile.getAbsolutePath()), Toast.LENGTH_LONG).show();
            return destinationFile;
        } catch (IOException e) {
            Log.e(TAG, "Error saving status (legacy): " + e.getMessage());
            Toast.makeText(context, R.string.download_failed, Toast.LENGTH_SHORT).show();
            return null;
        }
    }

     public static void shareMedia(Context context, Uri mediaUri) {
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType(context.getContentResolver().getType(mediaUri));
        shareIntent.putExtra(Intent.EXTRA_STREAM, mediaUri);
        shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        try {
            context.startActivity(Intent.createChooser(shareIntent, "Share via"));
        } catch (Exception e) {
            Toast.makeText(context, "No app found to share.", Toast.LENGTH_SHORT).show();
        }
    }
} 
package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.google.android.play.core.appupdate.AppUpdateInfo;
import com.google.android.play.core.appupdate.AppUpdateManager;
import com.google.android.play.core.appupdate.AppUpdateManagerFactory;
import com.google.android.play.core.install.model.UpdateAvailability;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.ks.app.service.statussaver.R;

/**
 * ✅ Global Update Manager - Singleton Pattern
 * Handles in-app updates globally across the entire application
 * Blocks ALL activities when update is required
 * Separate from splash screen logic for clean architecture
 */
public class GlobalUpdateManager {
    
    private static final String TAG = "GlobalUpdateManager";
    private static GlobalUpdateManager instance;
    
    // Update state management
    private boolean updateRequired = false;
    private boolean isCheckingUpdate = false;
    private boolean updateDialogShowing = false;
    
    // App update components
    private AppUpdateManager appUpdateManager;
    private Context applicationContext;
    
    // Current activity reference for dialog display
    private Activity currentActivity;
    
    /**
     * ✅ Singleton Instance
     */
    public static synchronized GlobalUpdateManager getInstance() {
        if (instance == null) {
            instance = new GlobalUpdateManager();
        }
        return instance;
    }
    
    /**
     * ✅ Initialize Global Update Manager
     * Call this from Application.onCreate()
     */
    public void initialize(Context context) {
        this.applicationContext = context.getApplicationContext();
        this.appUpdateManager = AppUpdateManagerFactory.create(applicationContext);
        
        Log.d(TAG, "🚀 GLOBAL UPDATE MANAGER: Initialized successfully");
        
        // Start initial update check
        checkForUpdateGlobally();
    }
    
    /**
     * ✅ Check if App is Blocked by Update Requirement
     * Called by BaseActivity to determine if screen should be blocked
     */
    public boolean isAppBlocked() {
        boolean blocked = updateRequired;
        Log.d(TAG, "🔍 APP BLOCK CHECK: App blocked = " + blocked);
        return blocked;
    }
    
    /**
     * ✅ Set Current Activity Reference
     * Called by BaseActivity to track current screen for dialog display
     */
    public void setCurrentActivity(Activity activity) {
        this.currentActivity = activity;
        Log.d(TAG, "📱 CURRENT ACTIVITY: Set to " + activity.getClass().getSimpleName());
    }
    
    /**
     * ✅ Clear Current Activity Reference
     * Called by BaseActivity onPause/onDestroy
     */
    public void clearCurrentActivity() {
        this.currentActivity = null;
        Log.d(TAG, "📱 CURRENT ACTIVITY: Cleared");
    }
    
    /**
     * ✅ Global Update Check
     * Checks for updates and sets global blocking state
     */
    public void checkForUpdateGlobally() {
        if (isCheckingUpdate) {
            Log.d(TAG, "🔄 UPDATE CHECK: Already in progress, skipping");
            return;
        }
        
        if (appUpdateManager == null) {
            Log.w(TAG, "❌ UPDATE CHECK: AppUpdateManager not initialized");
            return;
        }
        
        isCheckingUpdate = true;
        Log.d(TAG, "🔍 GLOBAL UPDATE CHECK: Starting update availability check");
        
        Task<AppUpdateInfo> appUpdateInfoTask = appUpdateManager.getAppUpdateInfo();
        
        appUpdateInfoTask.addOnCompleteListener(new OnCompleteListener<AppUpdateInfo>() {
            @Override
            public void onComplete(@NonNull Task<AppUpdateInfo> task) {
                isCheckingUpdate = false;
                
                if (task.isSuccessful()) {
                    AppUpdateInfo appUpdateInfo = task.getResult();
                    
                    if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                        Log.w(TAG, "🚨 UPDATE AVAILABLE: New version detected - BLOCKING APP");
                        Log.w(TAG, "📊 Available version code: " + appUpdateInfo.availableVersionCode());
                        
                        // Set global block state
                        updateRequired = true;
                        
                        // Show update dialog if activity is available
                        if (currentActivity != null && !updateDialogShowing) {
                            showMandatoryUpdateDialog();
                        }
                        
                    } else {
                        Log.d(TAG, "✅ NO UPDATE REQUIRED: App is up to date");
                        updateRequired = false;
                    }
                    
                } else {
                    Log.w(TAG, "❌ UPDATE CHECK FAILED: " + task.getException());
                    // Fail-safe: Don't block app if update check fails
                    updateRequired = false;
                }
            }
        });
    }
    
    /**
     * ✅ Show Mandatory Update Dialog
     * Blocks entire app until user updates or exits
     */
    public void showMandatoryUpdateDialog() {
        if (currentActivity == null) {
            Log.w(TAG, "⚠️ UPDATE DIALOG: No current activity available");
            return;
        }
        
        if (updateDialogShowing) {
            Log.d(TAG, "📋 UPDATE DIALOG: Already showing, skipping");
            return;
        }
        
        updateDialogShowing = true;
        Log.d(TAG, "📋 MANDATORY UPDATE DIALOG: Showing to user");
        
        // Create custom dialog layout
        LayoutInflater inflater = LayoutInflater.from(currentActivity);
        View dialogView = inflater.inflate(R.layout.dialog_mandatory_update, null);
        
        // Setup dialog components
        TextView titleText = dialogView.findViewById(R.id.update_title);
        TextView messageText = dialogView.findViewById(R.id.update_message);
        Button updateButton = dialogView.findViewById(R.id.btn_update_now);
        Button exitButton = dialogView.findViewById(R.id.btn_exit_app);
        
        titleText.setText("Update Required");
        messageText.setText("A new version of SavePro is available. Please update to continue using the app.");
        
        // Create non-cancelable dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(currentActivity);
        builder.setView(dialogView);
        builder.setCancelable(false);
        
        AlertDialog dialog = builder.create();
        
        // Update button click
        updateButton.setOnClickListener(v -> {
            Log.d(TAG, "👆 UPDATE BUTTON: User chose to update");
            redirectToPlayStore();
            dialog.dismiss();
            updateDialogShowing = false;
        });
        
        // Exit button click
        exitButton.setOnClickListener(v -> {
            Log.d(TAG, "👆 EXIT BUTTON: User chose to exit app");
            dialog.dismiss();
            updateDialogShowing = false;
            forceExitApp();
        });
        
        // Show dialog
        dialog.show();
        
        Log.d(TAG, "📋 UPDATE DIALOG: Displayed successfully");
    }
    
    /**
     * ✅ Redirect to Play Store for Update
     */
    private void redirectToPlayStore() {
        try {
            String packageName = applicationContext.getPackageName();
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=" + packageName));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            applicationContext.startActivity(intent);
            
            Log.d(TAG, "🏪 PLAY STORE: Redirected successfully");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ PLAY STORE: Failed to redirect", e);
            
            // Fallback to web browser
            try {
                String packageName = applicationContext.getPackageName();
                Intent intent = new Intent(Intent.ACTION_VIEW, 
                    Uri.parse("https://play.google.com/store/apps/details?id=" + packageName));
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                applicationContext.startActivity(intent);
                
                Log.d(TAG, "🌐 WEB BROWSER: Fallback redirect successful");
                
            } catch (Exception ex) {
                Log.e(TAG, "❌ WEB BROWSER: Fallback failed", ex);
            }
        }
    }
    
    /**
     * ✅ Force Exit App
     * Completely closes the application
     */
    private void forceExitApp() {
        Log.d(TAG, "🚪 FORCE EXIT: Closing application");
        
        if (currentActivity != null) {
            currentActivity.finishAffinity();
        }
        
        // Force system exit
        System.exit(0);
    }
    
    /**
     * ✅ Handle Activity Block
     * Called by BaseActivity when app is blocked
     */
    public void handleActivityBlock(Activity activity) {
        Log.d(TAG, "🛑 ACTIVITY BLOCKED: " + activity.getClass().getSimpleName());
        
        setCurrentActivity(activity);
        
        if (!updateDialogShowing) {
            showMandatoryUpdateDialog();
        }
    }
}

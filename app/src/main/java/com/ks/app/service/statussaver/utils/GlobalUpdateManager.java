package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

/**
 * ✅ Global Update Manager - <PERSON><PERSON> Pattern
 * Uses existing InAppUpdateHelper for consistent UI and behavior
 * Makes in-app updates work globally across ALL activities
 * Reuses your existing working update code
 */
public class GlobalUpdateManager {

    private static final String TAG = "GlobalUpdateManager";
    private static GlobalUpdateManager instance;

    // Update state management
    private boolean updateRequired = false;
    private boolean isCheckingUpdate = false;

    // Application context
    private Context applicationContext;

    // Current activity reference and its update helper
    private Activity currentActivity;
    private InAppUpdateHelper currentUpdateHelper;
    
    /**
     * ✅ Singleton Instance
     */
    public static synchronized GlobalUpdateManager getInstance() {
        if (instance == null) {
            instance = new GlobalUpdateManager();
        }
        return instance;
    }

    /**
     * ✅ Initialize Global Update Manager
     * Call this from Application.onCreate()
     */
    public void initialize(Context context) {
        this.applicationContext = context.getApplicationContext();

        Log.d(TAG, "🚀 GLOBAL UPDATE MANAGER: Initialized successfully");
        Log.d(TAG, "🔄 USING EXISTING: InAppUpdateHelper for consistent UI");

        // Note: We'll create InAppUpdateHelper instances per activity
        // This ensures proper lifecycle management
    }
    
    /**
     * ✅ Check if App is Blocked by Update Requirement
     * Called by BaseActivity to determine if screen should be blocked
     */
    public boolean isAppBlocked() {
        boolean blocked = updateRequired;
        Log.d(TAG, "🔍 APP BLOCK CHECK: App blocked = " + blocked);
        return blocked;
    }

    /**
     * ✅ Set Current Activity Reference
     * Called by BaseActivity to track current screen and create update helper
     */
    public void setCurrentActivity(Activity activity) {
        this.currentActivity = activity;

        // Create InAppUpdateHelper for this activity
        if (activity != null) {
            this.currentUpdateHelper = new InAppUpdateHelper(activity);
            Log.d(TAG, "📱 CURRENT ACTIVITY: Set to " + activity.getClass().getSimpleName());
            Log.d(TAG, "🔄 UPDATE HELPER: Created for " + activity.getClass().getSimpleName());
        }
    }

    /**
     * ✅ Clear Current Activity Reference
     * Called by BaseActivity onPause/onDestroy
     */
    public void clearCurrentActivity() {
        this.currentActivity = null;
        this.currentUpdateHelper = null;
        Log.d(TAG, "📱 CURRENT ACTIVITY: Cleared");
        Log.d(TAG, "🔄 UPDATE HELPER: Cleared");
    }
    
    /**
     * ✅ Global Update Check
     * Uses existing InAppUpdateHelper for consistent behavior
     */
    public void checkForUpdateGlobally() {
        if (isCheckingUpdate) {
            Log.d(TAG, "🔄 UPDATE CHECK: Already in progress, skipping");
            return;
        }

        if (currentActivity == null || currentUpdateHelper == null) {
            Log.d(TAG, "⏳ UPDATE CHECK: No current activity, will check when activity is set");
            return;
        }

        isCheckingUpdate = true;
        Log.d(TAG, "🔍 GLOBAL UPDATE CHECK: Using existing InAppUpdateHelper");

        // Use existing InAppUpdateHelper with callback
        currentUpdateHelper.startImmediateUpdateWithCallback(new InAppUpdateHelper.UpdateCallback() {
            @Override
            public void onUpdateAvailable() {
                Log.w(TAG, "🚨 UPDATE AVAILABLE: Blocking app globally");
                updateRequired = true;
                isCheckingUpdate = false;

                // The InAppUpdateHelper already shows the update UI
                // No need to show additional dialog
                Log.d(TAG, "📺 UPDATE UI: Shown by InAppUpdateHelper");
            }

            @Override
            public void onNoUpdateNeeded() {
                Log.d(TAG, "✅ NO UPDATE REQUIRED: App is up to date");
                updateRequired = false;
                isCheckingUpdate = false;
            }

            @Override
            public void onUpdateCheckFailed() {
                Log.w(TAG, "❌ UPDATE CHECK FAILED: Continuing without blocking");
                updateRequired = false;
                isCheckingUpdate = false;
            }
        });
    }
    
    /**
     * ✅ Handle Activity Block
     * Called by BaseActivity when app is blocked
     * Uses existing InAppUpdateHelper for update UI
     */
    public void handleActivityBlock(Activity activity) {
        Log.d(TAG, "🛑 ACTIVITY BLOCKED: " + activity.getClass().getSimpleName());

        setCurrentActivity(activity);

        // Use existing InAppUpdateHelper to show update UI
        if (currentUpdateHelper != null) {
            Log.d(TAG, "🔄 TRIGGERING UPDATE: Using existing InAppUpdateHelper");
            currentUpdateHelper.startImmediateUpdate();
        } else {
            Log.w(TAG, "⚠️ NO UPDATE HELPER: Cannot show update UI");
        }
    }

    /**
     * ✅ Resume Update If In Progress
     * Called by BaseActivity to handle interrupted updates
     */
    public void resumeUpdateIfInProgress() {
        if (currentUpdateHelper != null) {
            Log.d(TAG, "🔄 RESUME UPDATE: Checking for interrupted updates");
            currentUpdateHelper.resumeUpdateIfInProgress();
        }
    }

    /**
     * ✅ Handle Activity Result
     * Called by BaseActivity to handle update results
     */
    public boolean handleActivityResult(int requestCode, int resultCode) {
        if (currentUpdateHelper != null) {
            boolean handled = currentUpdateHelper.handleActivityResult(requestCode, resultCode);
            if (handled) {
                Log.d(TAG, "📋 UPDATE RESULT: Handled by InAppUpdateHelper");
            }
            return handled;
        }
        return false;
    }
}

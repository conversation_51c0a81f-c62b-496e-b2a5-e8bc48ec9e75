package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

/**
 * ✅ Global Update Manager - Centralized Update Detection
 * Automatically detects updates across the entire app
 * Uses existing InAppUpdateHelper without any modifications
 * Centralized management - no extra code needed in individual screens
 */
public class GlobalUpdateManager {

    private static final String TAG = "GlobalUpdateManager";
    private static GlobalUpdateManager instance;

    // Application context
    private Context applicationContext;

    // Current activity reference and its update helper
    private Activity currentActivity;
    private InAppUpdateHelper currentUpdateHelper;

    // Automatic update checking
    private boolean autoUpdateCheckEnabled = true;
    
    /**
     * ✅ Singleton Instance
     */
    public static synchronized GlobalUpdateManager getInstance() {
        if (instance == null) {
            instance = new GlobalUpdateManager();
        }
        return instance;
    }

    /**
     * ✅ Initialize Global Update Manager
     * Call this from Application.onCreate()
     */
    public void initialize(Context context) {
        this.applicationContext = context.getApplicationContext();

        Log.d(TAG, "🚀 GLOBAL UPDATE MANAGER: Initialized successfully");
        Log.d(TAG, "🔄 CENTRALIZED DETECTION: Will automatically use existing InAppUpdateHelper");
        Log.d(TAG, "✅ NO EXTRA CODE: Individual screens don't need update logic");
    }
    
    /**
     * ✅ Enable/Disable Automatic Update Checking
     * Can be used to temporarily disable update checks if needed
     */
    public void setAutoUpdateCheckEnabled(boolean enabled) {
        this.autoUpdateCheckEnabled = enabled;
        Log.d(TAG, "� AUTO UPDATE CHECK: " + (enabled ? "ENABLED" : "DISABLED"));
    }

    /**
     * ✅ Set Current Activity Reference
     * Automatically creates InAppUpdateHelper and triggers update check
     */
    public void setCurrentActivity(Activity activity) {
        this.currentActivity = activity;

        // Create InAppUpdateHelper for this activity
        if (activity != null) {
            this.currentUpdateHelper = new InAppUpdateHelper(activity);
            Log.d(TAG, "📱 CURRENT ACTIVITY: Set to " + activity.getClass().getSimpleName());
            Log.d(TAG, "🔄 UPDATE HELPER: Created for " + activity.getClass().getSimpleName());

            // Automatically trigger update check for this activity
            if (autoUpdateCheckEnabled) {
                triggerAutomaticUpdateCheck();
            }
        }
    }

    /**
     * ✅ Clear Current Activity Reference
     * Called by BaseActivity onPause/onDestroy
     */
    public void clearCurrentActivity() {
        this.currentActivity = null;
        this.currentUpdateHelper = null;
        Log.d(TAG, "📱 CURRENT ACTIVITY: Cleared");
        Log.d(TAG, "🔄 UPDATE HELPER: Cleared");
    }

    /**
     * ✅ Trigger Automatic Update Check
     * Uses existing InAppUpdateHelper.startImmediateUpdate() method
     * This is your existing update logic - no modifications needed
     */
    private void triggerAutomaticUpdateCheck() {
        if (currentUpdateHelper == null) {
            Log.w(TAG, "⚠️ AUTOMATIC UPDATE: No update helper available");
            return;
        }

        Log.d(TAG, "🔄 AUTOMATIC UPDATE CHECK: Triggering existing InAppUpdateHelper logic");
        Log.d(TAG, "✅ REUSING EXISTING CODE: No modifications to your working update system");

        // Use your existing update method - no changes needed
        currentUpdateHelper.startImmediateUpdate();
    }
    
    /**
     * ✅ Resume Update If In Progress
     * Uses existing InAppUpdateHelper.resumeUpdateIfInProgress() method
     */
    public void resumeUpdateIfInProgress() {
        if (currentUpdateHelper != null) {
            Log.d(TAG, "🔄 RESUME UPDATE: Using existing InAppUpdateHelper logic");
            currentUpdateHelper.resumeUpdateIfInProgress();
        }
    }

    /**
     * ✅ Handle Activity Result
     * Uses existing InAppUpdateHelper.handleActivityResult() method
     */
    public boolean handleActivityResult(int requestCode, int resultCode) {
        if (currentUpdateHelper != null) {
            boolean handled = currentUpdateHelper.handleActivityResult(requestCode, resultCode);
            if (handled) {
                Log.d(TAG, "📋 UPDATE RESULT: Handled by existing InAppUpdateHelper");
            }
            return handled;
        }
        return false;
    }
}

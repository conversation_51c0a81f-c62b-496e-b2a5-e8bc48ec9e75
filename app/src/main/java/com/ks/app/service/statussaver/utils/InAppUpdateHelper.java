package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.content.IntentSender;
import android.util.Log;

import com.google.android.play.core.appupdate.*;
import com.google.android.play.core.install.model.*;

/**
 * ✅ PRODUCTION-READY IN-APP UPDATE HELPER
 * 
 * Scalable, efficient implementation of Google Play In-App Update using best practices:
 * 
 * Features:
 * ✅ Fullscreen official UI via AppUpdateType.IMMEDIATE
 * ✅ Exit app if user cancels the update
 * ✅ Scalable, reusable helper class
 * ✅ Clean separation of concerns
 * ✅ Lifecycle-aware update resume
 * ✅ Easy to plug into any Activity
 * ✅ Play Store compatibility only
 * 
 * Usage:
 * 1. Create instance: updateHelper = new InAppUpdateHelper(this);
 * 2. Check updates: updateHelper.startImmediateUpdate();
 * 3. Resume updates: updateHelper.resumeUpdateIfInProgress();
 * 4. Handle result: updateHelper.handleActivityResult(requestCode, resultCode);
 */
public class InAppUpdateHelper {

    private static final String TAG = "InAppUpdateHelper";
    private static final int REQUEST_CODE = 9001;

    private final AppUpdateManager appUpdateManager;
    private final Activity activity;

    /**
     * Constructor - Initialize with activity context
     */
    public InAppUpdateHelper(Activity activity) {
        this.activity = activity;
        this.appUpdateManager = AppUpdateManagerFactory.create(activity);
    }

    /**
     * ✅ Update Callback Interface
     * Used for splash screen flow with parallel update check and ad loading
     *
     * Flow Logic:
     * - onUpdateAvailable() → Show update UI and STOP all navigation
     * - onNoUpdateNeeded() → Continue waiting for ad result
     * - onUpdateCheckFailed() → Continue waiting for ad result (fail-safe)
     */
    public interface UpdateCallback {
        /**
         * 🚨 Update is available - STOP the flow
         * Show update screen and do not proceed to ad or next screen
         */
        void onUpdateAvailable();

        /**
         * ✅ No update needed - Continue flow
         * Wait for ad loading to complete, then proceed to next screen
         */
        void onNoUpdateNeeded();

        /**
         * ❌ Update check failed - Continue flow (fail-safe)
         * Treat as "no update needed" and wait for ad loading to complete
         */
        void onUpdateCheckFailed();
    }

    /**
     * ✅ SPLASH SCREEN UPDATE CHECK
     *
     * Performs in-app update check during splash screen loading.
     * Runs in parallel with ad loading for optimal performance.
     *
     * Flow Logic:
     * 1. If update available → Show update UI and STOP splash flow
     * 2. If no update needed → Continue splash flow (wait for ad)
     * 3. If check fails → Continue splash flow (fail-safe behavior)
     *
     * Only works when app is installed from Play Store.
     * Automatically treated as "no update" for sideloaded apps.
     */
    public void startImmediateUpdateWithCallback(UpdateCallback callback) {
        Log.d(TAG, "🔄 SPLASH FLOW: Starting in-app update check (parallel with ad loading)");

        // Only check if app is installed from Play Store
        if (!isInstalledFromPlayStore()) {
            Log.d(TAG, "📱 SPLASH FLOW: App not from Play Store - treating as 'no update needed'");
            if (callback != null) callback.onNoUpdateNeeded();
            return;
        }

        appUpdateManager.getAppUpdateInfo().addOnSuccessListener(appUpdateInfo -> {
            Log.d(TAG, "✅ SPLASH FLOW: Update info retrieved");
            Log.d(TAG, "   📋 Availability: " + appUpdateInfo.updateAvailability());
            Log.d(TAG, "   📋 Priority: " + appUpdateInfo.updatePriority());
            Log.d(TAG, "   📋 Available version: " + appUpdateInfo.availableVersionCode());

            if (shouldStartImmediateUpdate(appUpdateInfo)) {
                Log.d(TAG, "🚨 SPLASH FLOW: Update available - STOPPING splash flow and showing update UI");

                // Notify splash screen to STOP the flow
                if (callback != null) callback.onUpdateAvailable();

                try {
                    // Show full-screen update UI
                    appUpdateManager.startUpdateFlowForResult(
                            appUpdateInfo,
                            AppUpdateType.IMMEDIATE,
                            activity,
                            REQUEST_CODE
                    );
                    Log.d(TAG, "🎬 SPLASH FLOW: Update UI launched successfully");
                } catch (IntentSender.SendIntentException e) {
                    Log.e(TAG, "❌ SPLASH FLOW: Failed to launch update UI: " + e.getMessage());
                    // Critical failure - exit app
                    exitApp();
                }
            } else {
                Log.d(TAG, "✅ SPLASH FLOW: No update needed - continuing to wait for ad result");
                if (callback != null) callback.onNoUpdateNeeded();
            }
        }).addOnFailureListener(e -> {
            Log.e(TAG, "❌ SPLASH FLOW: Update check failed: " + e.getMessage());
            Log.d(TAG, "🛡️ SPLASH FLOW: Fail-safe behavior - continuing to wait for ad result");
            // Fail-safe: Continue flow even if update check fails
            if (callback != null) callback.onUpdateCheckFailed();
        });
    }

    /**
     * ✅ TRIGGER IMMEDIATE UPDATE
     *
     * Starts Google Play's full-screen in-app update.
     * Exits the app if user cancels (critical update behavior).
     *
     * Only works when app is installed from Play Store.
     * Automatically ignored for sideloaded apps.
     */
    public void startImmediateUpdate() {
        Log.d(TAG, "🔄 Starting immediate update check");

        // Only check if app is installed from Play Store
        if (!isInstalledFromPlayStore()) {
            Log.d(TAG, "📱 App not installed from Play Store - skipping update check");
            return;
        }

        appUpdateManager.getAppUpdateInfo().addOnSuccessListener(appUpdateInfo -> {
            Log.d(TAG, "✅ Update info retrieved - Availability: " + appUpdateInfo.updateAvailability());
            Log.d(TAG, "📋 Update priority: " + appUpdateInfo.updatePriority());
            Log.d(TAG, "📋 Available version code: " + appUpdateInfo.availableVersionCode());

            if (shouldStartImmediateUpdate(appUpdateInfo)) {
                Log.d(TAG, "🚀 Critical update available - Starting IMMEDIATE update flow");

                try {
                    appUpdateManager.startUpdateFlowForResult(
                            appUpdateInfo,
                            AppUpdateType.IMMEDIATE,
                            activity,
                            REQUEST_CODE
                    );
                } catch (IntentSender.SendIntentException e) {
                    Log.e(TAG, "❌ Failed to launch immediate update: " + e.getMessage());
                    // Exit app if update launch fails for critical updates
                    exitApp();
                }
            } else {
                Log.d(TAG, "✅ No immediate update needed - App is up to date");
            }
        }).addOnFailureListener(e -> {
            Log.e(TAG, "❌ Update check failed: " + e.getMessage());
            // Continue without update if check fails (network issues, etc.)
            // Don't exit for update check failures - just log
        });
    }

    /**
     * ✅ RESUME INTERRUPTED UPDATE OR EXIT IF CANCELLED
     *
     * Resume interrupted update flow, e.g. after app restart.
     * If user cancelled update, exits the app.
     * Call this in onResume() to handle interrupted updates.
     */
    public void resumeUpdateIfInProgress() {
        Log.d(TAG, "🔄 Checking for interrupted update");

        appUpdateManager.getAppUpdateInfo().addOnSuccessListener(appUpdateInfo -> {
            Log.d(TAG, "📋 Update status: " + appUpdateInfo.updateAvailability());

            if (appUpdateInfo.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                Log.d(TAG, "📱 Resuming interrupted update");

                try {
                    appUpdateManager.startUpdateFlowForResult(
                            appUpdateInfo,
                            AppUpdateType.IMMEDIATE,
                            activity,
                            REQUEST_CODE
                    );
                } catch (IntentSender.SendIntentException e) {
                    Log.e(TAG, "❌ Failed to resume interrupted update: " + e.getMessage());
                    exitApp();
                }
            } else if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE &&
                       appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)) {
                // User returned without updating - this means they cancelled
                Log.e(TAG, "❌ User cancelled the update - exiting app");
                exitApp();
            } else {
                Log.d(TAG, "✅ No interrupted update to resume");
            }
        }).addOnFailureListener(e -> {
            Log.e(TAG, "❌ Failed to check for interrupted update: " + e.getMessage());
        });
    }

    /**
     * ✅ HANDLE ACTIVITY RESULT
     *
     * Handle the result from update flow.
     * Exits app if user cancels critical update.
     *
     * Call this in onActivityResult() of your activity.
     *
     * @param requestCode The request code from onActivityResult
     * @param resultCode The result code from onActivityResult
     * @return true if this was an update result, false otherwise
     */
    public boolean handleActivityResult(int requestCode, int resultCode) {
        if (requestCode == REQUEST_CODE) {
            Log.d(TAG, "📋 Update result - Request: " + requestCode + ", Result: " + resultCode);

            switch (resultCode) {
                case Activity.RESULT_OK:
                    Log.d(TAG, "✅ Update completed successfully");
                    // Update was successful, continue normal app flow
                    break;

                case Activity.RESULT_CANCELED:
                    Log.e(TAG, "❌ Update cancelled by user - exiting app");
                    exitApp();
                    break;

                case ActivityResult.RESULT_IN_APP_UPDATE_FAILED:
                    Log.e(TAG, "❌ Update failed - exiting app");
                    exitApp();
                    break;

                default:
                    Log.e(TAG, "❌ Unknown update result: " + resultCode + " - exiting app");
                    exitApp();
                    break;
            }
            return true;
        }
        return false;
    }

    /**
     * Check if immediate update should be started
     */
    private boolean shouldStartImmediateUpdate(AppUpdateInfo appUpdateInfo) {
        return appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                && appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE);
    }

    /**
     * Check if app is installed from Play Store
     * Update APIs only work when installed via Play Store
     */
    private boolean isInstalledFromPlayStore() {
        try {
            String installer = activity.getPackageManager().getInstallerPackageName(activity.getPackageName());
            boolean isFromPlayStore = installer != null && installer.equals("com.android.vending");
            Log.d(TAG, "📦 App installer: " + installer + " (Play Store: " + isFromPlayStore + ")");
            return isFromPlayStore;
        } catch (Exception e) {
            Log.e(TAG, "❌ Error checking app installer: " + e.getMessage());
            return false;
        }
    }

    /**
     * ✅ EXIT APP FOR CRITICAL UPDATES
     * 
     * Gracefully exit the app when critical update is canceled.
     * This ensures users can't use outdated versions for security/compliance.
     */
    /**
     * ✅ EXIT APP
     *
     * Force exit the app when critical update is required.
     * This ensures users cannot bypass mandatory updates.
     */
    private void exitApp() {
        Log.e(TAG, "🚪 Exiting app due to mandatory update requirement");

        try {
            // Close all activities in the task stack
            activity.finishAffinity();

            // Give a moment for activities to finish gracefully
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                // Force kill the app process
                android.os.Process.killProcess(android.os.Process.myPid());
                System.exit(0);
            }, 500);

        } catch (Exception e) {
            Log.e(TAG, "Error during app exit: " + e.getMessage());
            // Fallback - immediate exit
            System.exit(0);
        }
    }
}

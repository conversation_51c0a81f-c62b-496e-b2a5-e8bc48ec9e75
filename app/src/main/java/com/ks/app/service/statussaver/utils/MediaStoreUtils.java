package com.ks.app.service.statussaver.utils;

import android.content.ContentUris;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import com.ks.app.service.statussaver.data.models.MediaItem;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MediaStoreUtils {
    private static final String TAG = "MediaStoreUtils";

    public static List<MediaItem> getAllGalleryMedia(Context context) {
        List<MediaItem> mediaItems = new ArrayList<>();

        try {
            Log.d(TAG, "Fetching all gallery media");

            List<MediaItem> images = getAllGalleryImages(context);
            List<MediaItem> videos = getAllGalleryVideos(context);

            if (images != null) {
                mediaItems.addAll(images);
                Log.d(TAG, "Added " + images.size() + " images");
            }

            if (videos != null) {
                mediaItems.addAll(videos);
                Log.d(TAG, "Added " + videos.size() + " videos");
            }

            // Sort by date modified (newest first)
            Collections.sort(mediaItems, (o1, o2) -> Long.compare(o2.getDateModified(), o1.getDateModified()));

            Log.d(TAG, "Total gallery media items: " + mediaItems.size());

        } catch (Exception e) {
            Log.e(TAG, "Error fetching all gallery media", e);
            return new ArrayList<>();
        }

        return mediaItems;
    }

    private static List<MediaItem> getAllGalleryImages(Context context) {
        List<MediaItem> imageItems = new ArrayList<>();
        Uri collection = Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q ?
                MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL) :
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI;

        String[] projection = {
                MediaStore.Images.Media._ID,
                MediaStore.Images.Media.DISPLAY_NAME,
                MediaStore.Images.Media.DATE_MODIFIED,
                MediaStore.Images.Media.MIME_TYPE,
                MediaStore.Images.Media.DATA // For file path if needed, but URI is preferred
        };
        String sortOrder = MediaStore.Images.Media.DATE_MODIFIED + " DESC";

        try (Cursor cursor = context.getContentResolver().query(collection, projection, null, null, sortOrder)) {
            if (cursor != null) {
                int idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID);
                int nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME);
                int dateModifiedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_MODIFIED);

                while (cursor.moveToNext()) {
                    long id = cursor.getLong(idColumn);
                    String name = cursor.getString(nameColumn);
                    // Handle null filename
                    if (name == null) {
                        name = "image_" + id;
                    }
                    long dateModified = cursor.getLong(dateModifiedColumn) * 1000;
                    Uri contentUri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id);
                    imageItems.add(new MediaItem(contentUri, name, false, dateModified));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error fetching gallery images: " + e.getMessage());
        }
        return imageItems;
    }

    private static List<MediaItem> getAllGalleryVideos(Context context) {
        List<MediaItem> videoItems = new ArrayList<>();
        Uri collection = Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q ?
                MediaStore.Video.Media.getContentUri(MediaStore.VOLUME_EXTERNAL) :
                MediaStore.Video.Media.EXTERNAL_CONTENT_URI;

        String[] projection = {
                MediaStore.Video.Media._ID,
                MediaStore.Video.Media.DISPLAY_NAME,
                MediaStore.Video.Media.DATE_MODIFIED,
                MediaStore.Video.Media.MIME_TYPE,
                MediaStore.Video.Media.DATA // For file path if needed
        };
        String sortOrder = MediaStore.Video.Media.DATE_MODIFIED + " DESC";

        try (Cursor cursor = context.getContentResolver().query(collection, projection, null, null, sortOrder)) {
            if (cursor != null) {
                int idColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media._ID);
                int nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DISPLAY_NAME);
                int dateModifiedColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_MODIFIED);

                while (cursor.moveToNext()) {
                    long id = cursor.getLong(idColumn);
                    String name = cursor.getString(nameColumn);
                    // Handle null filename
                    if (name == null) {
                        name = "video_" + id;
                    }
                    long dateModified = cursor.getLong(dateModifiedColumn) * 1000;
                    Uri contentUri = ContentUris.withAppendedId(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, id);
                    videoItems.add(new MediaItem(contentUri, name, true, dateModified));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error fetching gallery videos: " + e.getMessage());
        }
        return videoItems;
    }


} 
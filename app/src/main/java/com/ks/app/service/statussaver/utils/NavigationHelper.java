package com.ks.app.service.statussaver.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

import androidx.core.content.ContextCompat;

import com.ks.app.service.statussaver.ui.main.MainActivity;
import com.ks.app.service.statussaver.ui.main.SafPermissionActivity;

/**
 * ✅ Navigation Helper - Smart Permission-Based Navigation
 * Handles intelligent navigation based on permission status
 * Used by splash screen and other activities for consistent flow
 */
public class NavigationHelper {
    
    private static final String TAG = "NavigationHelper";
    
    /**
     * ✅ Check Permissions and Navigate Intelligently
     * Main method for smart navigation after splash/ad completion
     * 
     * @param activity Current activity context
     */
    public static void checkPermissionsAndNavigate(Activity activity) {
        Log.d(TAG, "🔍 SMART NAVIGATION: Checking permissions and determining destination");
        
        if (areMediaPermissionsGranted(activity)) {
            Log.d(TAG, "✅ PERMISSIONS GRANTED: Navigating directly to Home screen");
            navigateToHome(activity);
        } else {
            Log.d(TAG, "❌ PERMISSIONS DENIED: Navigating to Permission screen first");
            navigateToPermissions(activity);
        }
    }
    
    /**
     * ✅ Check if Media Permissions are Granted
     * Comprehensive check for all required media permissions
     * 
     * @param context Application context
     * @return true if all required permissions are granted
     */
    public static boolean areMediaPermissionsGranted(Context context) {
        Log.d(TAG, "🔍 PERMISSION CHECK: Checking media permissions");
        
        // For Android 13+ (API 33+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            boolean readImages = ContextCompat.checkSelfPermission(context, 
                Manifest.permission.READ_MEDIA_IMAGES) == PackageManager.PERMISSION_GRANTED;
            boolean readVideo = ContextCompat.checkSelfPermission(context, 
                Manifest.permission.READ_MEDIA_VIDEO) == PackageManager.PERMISSION_GRANTED;
            
            Log.d(TAG, "📱 ANDROID 13+: READ_MEDIA_IMAGES = " + readImages + 
                       ", READ_MEDIA_VIDEO = " + readVideo);
            
            return readImages && readVideo;
        }
        // For Android 6-12 (API 23-32)
        else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean readExternal = ContextCompat.checkSelfPermission(context, 
                Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
            
            Log.d(TAG, "📱 ANDROID 6-12: READ_EXTERNAL_STORAGE = " + readExternal);
            
            return readExternal;
        }
        // For Android 5 and below (API < 23)
        else {
            Log.d(TAG, "📱 ANDROID 5-: Permissions granted by default");
            return true; // Permissions granted at install time
        }
    }
    
    /**
     * ✅ Navigate to Home Screen (MainActivity)
     * Direct navigation to main app functionality
     * 
     * @param activity Current activity context
     */
    public static void navigateToHome(Activity activity) {
        Log.d(TAG, "🏠 NAVIGATING TO HOME: Starting MainActivity");
        
        try {
            Intent intent = new Intent(activity, MainActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            activity.startActivity(intent);
            
            // Smooth transition animation
            activity.overridePendingTransition(
                android.R.anim.fade_in, 
                android.R.anim.fade_out
            );
            
            activity.finish();
            
            Log.d(TAG, "✅ HOME NAVIGATION: Successfully started MainActivity");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ HOME NAVIGATION ERROR: " + e.getMessage(), e);
        }
    }
    
    /**
     * ✅ Navigate to Permission Screen (SafPermissionActivity)
     * Navigation to permission request screen
     * 
     * @param activity Current activity context
     */
    public static void navigateToPermissions(Activity activity) {
        Log.d(TAG, "🔐 NAVIGATING TO PERMISSIONS: Starting SafPermissionActivity");
        
        try {
            Intent intent = new Intent(activity, SafPermissionActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            activity.startActivity(intent);
            
            // Smooth transition animation
            activity.overridePendingTransition(
                android.R.anim.fade_in,
                android.R.anim.fade_out
            );
            
            activity.finish();
            
            Log.d(TAG, "✅ PERMISSION NAVIGATION: Successfully started SafPermissionActivity");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ PERMISSION NAVIGATION ERROR: " + e.getMessage(), e);
        }
    }
    
    /**
     * ✅ Navigate from Permission Screen to Home
     * Called after permissions are granted in SafPermissionActivity
     * 
     * @param activity Current activity context (SafPermissionActivity)
     */
    public static void navigateFromPermissionsToHome(Activity activity) {
        Log.d(TAG, "🔐➡️🏠 PERMISSION TO HOME: Navigating after permission grant");
        
        // Double-check permissions before navigating
        if (areMediaPermissionsGranted(activity)) {
            Log.d(TAG, "✅ PERMISSIONS CONFIRMED: Proceeding to home");
            navigateToHome(activity);
        } else {
            Log.w(TAG, "⚠️ PERMISSIONS NOT CONFIRMED: Staying on permission screen");
            // Stay on permission screen - user needs to grant permissions
        }
    }
    
    /**
     * ✅ Check Specific Permission
     * Utility method to check individual permissions
     * 
     * @param context Application context
     * @param permission Permission string to check
     * @return true if permission is granted
     */
    public static boolean isPermissionGranted(Context context, String permission) {
        boolean granted = ContextCompat.checkSelfPermission(context, permission) 
                         == PackageManager.PERMISSION_GRANTED;
        
        Log.d(TAG, "🔍 SINGLE PERMISSION CHECK: " + permission + " = " + granted);
        
        return granted;
    }
    
    /**
     * ✅ Get Required Permissions Array
     * Returns the appropriate permissions array based on Android version
     * 
     * @return Array of required permissions
     */
    public static String[] getRequiredPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ permissions
            String[] permissions = {
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO
            };
            
            Log.d(TAG, "📱 REQUIRED PERMISSIONS (Android 13+): " + permissions.length + " permissions");
            return permissions;
            
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6-12 permissions
            String[] permissions = {
                Manifest.permission.READ_EXTERNAL_STORAGE
            };
            
            Log.d(TAG, "📱 REQUIRED PERMISSIONS (Android 6-12): " + permissions.length + " permissions");
            return permissions;
            
        } else {
            // Android 5 and below - no runtime permissions needed
            Log.d(TAG, "📱 REQUIRED PERMISSIONS (Android 5-): No runtime permissions needed");
            return new String[0];
        }
    }
    
    /**
     * ✅ Log Permission Status
     * Utility method for debugging permission states
     * 
     * @param context Application context
     */
    public static void logPermissionStatus(Context context) {
        Log.d(TAG, "📊 PERMISSION STATUS REPORT:");
        Log.d(TAG, "   📱 Android Version: " + Build.VERSION.SDK_INT);
        Log.d(TAG, "   🔍 All Media Permissions Granted: " + areMediaPermissionsGranted(context));
        
        String[] permissions = getRequiredPermissions();
        for (String permission : permissions) {
            boolean granted = isPermissionGranted(context, permission);
            Log.d(TAG, "   📋 " + permission + ": " + (granted ? "✅ GRANTED" : "❌ DENIED"));
        }
    }
}

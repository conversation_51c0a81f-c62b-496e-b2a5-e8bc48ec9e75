package com.ks.app.service.statussaver.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import java.util.ArrayList;
import java.util.List;

/**
 * Optimized Permission Manager for WhatsApp Status Saver (2025)
 * ✅ Supports Android 13+ (Tiramisu) granular media permissions
 * ✅ Handles scoped storage properly
 * ✅ No deprecated permissions
 * ✅ Play Store compliant
 */
public class PermissionUtils {

    private static final String TAG = "PermissionUtils";
    public static final int PERMISSIONS_REQUEST_CODE = 101;
    private static boolean isRequestingPermissions = false;

    /**
     * Get required permissions based on Android version
     * Enhanced compatibility for all devices and Android versions
     */
    private static String[] getRequiredPermissions() {
        List<String> permissions = new ArrayList<>();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+) - Only request permissions we actually need
            permissions.add(Manifest.permission.READ_MEDIA_IMAGES);
            permissions.add(Manifest.permission.READ_MEDIA_VIDEO);
            // ❌ REMOVED READ_MEDIA_AUDIO - not needed for status saver and causes permission issues
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6-12 (API 23-32) - READ_EXTERNAL_STORAGE
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        } else {
            // Android 4.1-5.1 (API 16-22) - Both read and write for older devices
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }

        return permissions.toArray(new String[0]);
    }

    /**
     * Enhanced permission check with device-specific fallbacks
     */
    public static boolean hasPermissionsEnhanced(Context context) {
        try {
            if (context == null) {
                Log.e(TAG, "Context is null in hasPermissionsEnhanced");
                return false;
            }

            // For very old Android versions (below API 16), no runtime permissions needed
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN) {
                return true;
            }

            String[] requiredPermissions = getRequiredPermissions();
            if (requiredPermissions == null || requiredPermissions.length == 0) {
                return true;
            }

            // Check each permission
            for (String permission : requiredPermissions) {
                if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    Log.d(TAG, "Missing permission: " + permission);
                    return false;
                }
            }

            // Additional device-specific checks
            return performDeviceSpecificChecks(context);

        } catch (Exception e) {
            Log.e(TAG, "Error in hasPermissionsEnhanced: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * Device-specific permission checks for problematic manufacturers
     */
    private static boolean performDeviceSpecificChecks(Context context) {
        try {
            String manufacturer = Build.MANUFACTURER.toLowerCase();
            String model = Build.MODEL.toLowerCase();

            Log.d(TAG, "Device check - Manufacturer: " + manufacturer + ", Model: " + model);

            // Xiaomi devices often have additional permission layers
            if (manufacturer.contains("xiaomi") || manufacturer.contains("redmi")) {
                return checkXiaomiPermissions(context);
            }

            // Huawei devices may have additional restrictions
            if (manufacturer.contains("huawei") || manufacturer.contains("honor")) {
                return checkHuaweiPermissions(context);
            }

            // Samsung devices generally work well with standard permissions
            if (manufacturer.contains("samsung")) {
                return checkSamsungPermissions(context);
            }

            // OnePlus devices
            if (manufacturer.contains("oneplus")) {
                return checkOnePlusPermissions(context);
            }

            // Default case for other manufacturers
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error in device-specific checks: " + e.getMessage(), e);
            return true; // Default to true to avoid blocking
        }
    }

    private static boolean checkXiaomiPermissions(Context context) {
        // Xiaomi devices often require additional checks
        Log.d(TAG, "Performing Xiaomi-specific permission checks");
        return true; // For now, return true but log for monitoring
    }

    private static boolean checkHuaweiPermissions(Context context) {
        // Huawei devices may have additional restrictions
        Log.d(TAG, "Performing Huawei-specific permission checks");
        return true;
    }

    private static boolean checkSamsungPermissions(Context context) {
        // Samsung devices generally work well
        Log.d(TAG, "Performing Samsung-specific permission checks");
        return true;
    }

    private static boolean checkOnePlusPermissions(Context context) {
        // OnePlus devices
        Log.d(TAG, "Performing OnePlus-specific permission checks");
        return true;
    }

    /**
     * ✅ SIMPLIFIED PERMISSION CHECK - More reliable detection
     *
     * Check if all required permissions are granted.
     * Safe to call from onResume() or any lifecycle method.
     * Uses direct permission checking for better reliability.
     */
    public static boolean hasPermissions(Context context) {
        try {
            Log.d(TAG, "🔍 Checking permissions for Android " + Build.VERSION.SDK_INT);

            if (context == null) {
                Log.e(TAG, "Context is null in hasPermissions");
                return false;
            }

            // For very old Android versions (below API 16), no runtime permissions needed
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN) {
                Log.d(TAG, "✅ Android < 16: No runtime permissions needed");
                return true;
            }

            String[] requiredPermissions = getRequiredPermissions();
            if (requiredPermissions == null || requiredPermissions.length == 0) {
                Log.d(TAG, "✅ No permissions required for this Android version");
                return true;
            }

            // Check each required permission with detailed logging
            for (String permission : requiredPermissions) {
                if (permission != null) {
                    int permissionStatus = ContextCompat.checkSelfPermission(context, permission);
                    boolean isGranted = (permissionStatus == PackageManager.PERMISSION_GRANTED);
                    Log.d(TAG, "Permission " + permission + ": " + (isGranted ? "✅ GRANTED" : "❌ DENIED"));

                    if (!isGranted) {
                        Log.d(TAG, "❌ Missing permission: " + permission);
                        return false;
                    }
                }
            }

            Log.d(TAG, "✅ All permissions granted");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error in hasPermissions: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * Basic permission check as fallback
     */
    private static boolean hasPermissionsBasic(Context context) {
        try {
            if (context == null) {
                Log.e(TAG, "Context is null in hasPermissionsBasic");
                return false;
            }

            String[] requiredPermissions = null;
            try {
                requiredPermissions = getRequiredPermissions();
            } catch (Exception e) {
                Log.e(TAG, "Error getting required permissions: " + e.getMessage(), e);
                return false;
            }

            // If no permissions required for this Android version, return true
            if (requiredPermissions == null || requiredPermissions.length == 0) {
                return true;
            }

            // Check each required permission
            for (String permission : requiredPermissions) {
                if (permission != null) {
                    try {
                        int permissionStatus = ContextCompat.checkSelfPermission(context, permission);
                        if (permissionStatus != PackageManager.PERMISSION_GRANTED) {
                            return false;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking permission " + permission + ": " + e.getMessage(), e);
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error in hasPermissions: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * Request missing permissions
     * Prevents duplicate requests and handles all Android versions
     */
    public static void requestPermissions(Activity activity) {
        try {
            if (activity == null) {
                Log.e(TAG, "Activity is null in requestPermissions");
                return;
            }

            // Prevent multiple simultaneous requests
            if (isRequestingPermissions) {
                return;
            }

            String[] requiredPermissions = null;
            try {
                requiredPermissions = getRequiredPermissions();
            } catch (Exception e) {
                Log.e(TAG, "Error getting required permissions for request: " + e.getMessage(), e);
                return;
            }

            if (requiredPermissions == null) {
                Log.e(TAG, "Required permissions is null");
                return;
            }

            List<String> permissionsToRequest = new ArrayList<>();

            // Find missing permissions
            for (String permission : requiredPermissions) {
                if (permission != null) {
                    try {
                        int permissionStatus = ContextCompat.checkSelfPermission(activity, permission);
                        if (permissionStatus != PackageManager.PERMISSION_GRANTED) {
                            permissionsToRequest.add(permission);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking permission " + permission + " for request: " + e.getMessage(), e);
                    }
                }
            }

            // Request missing permissions
            if (!permissionsToRequest.isEmpty()) {
                try {
                    isRequestingPermissions = true;
                    ActivityCompat.requestPermissions(
                        activity,
                        permissionsToRequest.toArray(new String[0]),
                        PERMISSIONS_REQUEST_CODE
                    );
                } catch (Exception e) {
                    Log.e(TAG, "Error requesting permissions: " + e.getMessage(), e);
                    isRequestingPermissions = false; // Reset flag on error
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in requestPermissions: " + e.getMessage(), e);
            isRequestingPermissions = false; // Reset flag on error
        }
    }

    // Call this from Activity's onRequestPermissionsResult
    public static void onRequestPermissionsResultReceived() {
        try {
            isRequestingPermissions = false;
        } catch (Exception e) {
            Log.e(TAG, "Error in onRequestPermissionsResultReceived: " + e.getMessage(), e);
        }
    }

    public static boolean isPermissionRequestInProgress(Context context) { // Context not strictly needed but good for consistency
        try {
            return isRequestingPermissions;
        } catch (Exception e) {
            Log.e(TAG, "Error in isPermissionRequestInProgress: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * ✅ IMPROVED RATIONALE CHECK - Better permission denial detection
     *
     * Check if we should show permission rationale (user previously denied).
     * This helps determine if user denied permissions and we should show "Open Settings".
     */
    public static boolean shouldShowRationale(Activity activity) {
        try {
            Log.d(TAG, "🔍 Checking if should show permission rationale");

            if (activity == null) {
                Log.e(TAG, "Activity is null in shouldShowRationale");
                return false;
            }

            String[] requiredPermissions = getRequiredPermissions();
            if (requiredPermissions == null || requiredPermissions.length == 0) {
                Log.d(TAG, "No permissions to check rationale for");
                return false;
            }

            boolean shouldShow = false;
            for (String permission : requiredPermissions) {
                if (permission != null) {
                    boolean rationale = ActivityCompat.shouldShowRequestPermissionRationale(activity, permission);
                    Log.d(TAG, "Permission " + permission + " rationale: " + rationale);

                    if (rationale) {
                        shouldShow = true;
                    }
                }
            }

            Log.d(TAG, "Should show rationale: " + shouldShow);
            return shouldShow;

        } catch (Exception e) {
            Log.e(TAG, "Error in shouldShowRationale: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * ✅ DEBUG METHOD - Test permission detection
     *
     * Call this method to debug permission issues.
     * Logs detailed information about permission status.
     */
    public static void debugPermissions(Context context) {
        try {
            Log.d(TAG, "🔍 === PERMISSION DEBUG START ===");
            Log.d(TAG, "Android Version: " + Build.VERSION.SDK_INT);

            String[] requiredPermissions = getRequiredPermissions();
            Log.d(TAG, "Required permissions count: " + (requiredPermissions != null ? requiredPermissions.length : 0));

            if (requiredPermissions != null) {
                for (String permission : requiredPermissions) {
                    if (permission != null) {
                        int status = ContextCompat.checkSelfPermission(context, permission);
                        String statusText = (status == PackageManager.PERMISSION_GRANTED) ? "✅ GRANTED" : "❌ DENIED";
                        Log.d(TAG, "Permission: " + permission + " → " + statusText);
                    }
                }
            }

            boolean hasAll = hasPermissions(context);
            Log.d(TAG, "Overall permission status: " + (hasAll ? "✅ ALL GRANTED" : "❌ MISSING PERMISSIONS"));
            Log.d(TAG, "🔍 === PERMISSION DEBUG END ===");

        } catch (Exception e) {
            Log.e(TAG, "Error in debugPermissions: " + e.getMessage(), e);
        }
    }
}
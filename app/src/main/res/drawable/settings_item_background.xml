<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/settings_item_pressed" />
            <stroke 
                android:width="1dp" 
                android:color="@color/settings_border_color" />
        </shape>
    </item>
    
    <!-- Selected state -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/settings_item_selected" />
            <stroke 
                android:width="1dp" 
                android:color="@color/settings_border_color" />
        </shape>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/settings_item_background" />
            <stroke 
                android:width="1dp" 
                android:color="@color/settings_border_color" />
        </shape>
    </item>
    
</selector>

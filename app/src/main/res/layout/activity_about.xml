<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/splash_background">

    <!-- Header with back button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <ImageView
            android:id="@+id/back_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_menu_revert"
            android:tint="@android:color/white"
            android:contentDescription="Back"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="About"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="start">

        <TextView
            android:id="@+id/content_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:lineSpacingExtra="4dp"
            android:gravity="start"
            android:layout_marginBottom="24dp"
            android:text="Loading..." />

        <TextView
            android:id="@+id/email_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/holo_blue_light"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="?android:attr/selectableItemBackground"
            android:padding="12dp"
            android:text="📧 <EMAIL>" />



    </LinearLayout>

</LinearLayout>

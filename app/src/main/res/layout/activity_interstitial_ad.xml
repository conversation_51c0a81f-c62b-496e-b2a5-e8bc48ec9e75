<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <!-- Loading Container -->
    <LinearLayout
        android:id="@+id/loading_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:background="#000000"
        android:padding="32dp">

        <!-- App Logo -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_launcher_foreground"
            android:layout_marginBottom="32dp"
            android:scaleType="centerInside" />

        <!-- App Name -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="SavePro – Status Downloader"
            android:textColor="#FFFFFF"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- Loading Text -->
        <TextView
            android:id="@+id/loading_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Loading amazing content..."
            android:textColor="#B3FFFFFF"
            android:textSize="16sp"
            android:gravity="center"
            android:layout_marginBottom="32dp" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="200dp"
            android:layout_height="4dp"
            android:indeterminate="true"
            android:progressTint="#00DCB2"
            android:layout_marginBottom="24dp" />

        <!-- Loading Dots Animation -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <View
                android:id="@+id/dot1"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/loading_dot"
                android:layout_marginEnd="8dp" />

            <View
                android:id="@+id/dot2"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/loading_dot"
                android:layout_marginEnd="8dp" />

            <View
                android:id="@+id/dot3"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/loading_dot" />

        </LinearLayout>

        <!-- Bottom Text -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Preparing your experience..."
            android:textColor="#80FFFFFF"
            android:textSize="14sp"
            android:gravity="center"
            android:layout_marginTop="32dp" />

    </LinearLayout>

</FrameLayout>

<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#101B23"
    android:padding="24dp">

    <!-- Title -->
    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Allow Permission"
        android:textColor="#FFFFFF"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_alignParentTop="true" />

    <!-- Main Container with dark background - Content height only -->
    <LinearLayout
        android:id="@+id/mainContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#1A252F"
        android:padding="24dp"
        android:layout_below="@id/title"
        android:layout_marginTop="24dp"
        android:layout_centerInParent="true">

        <!-- Folder Path inside card at top left -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Internal > Android > media"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:layout_marginBottom="24dp"
            android:gravity="start" />

        <!-- Dialog Card -->
        <RelativeLayout
            android:id="@+id/cardInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rounded_dark"
            android:padding="20dp"
            android:layout_marginBottom="24dp">

            <TextView
                android:id="@+id/dialogTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Allow SavePro – Status Downloader to access files in media?"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/dialogDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="This will let SavePro – Status Downloader access current and future content stored in media."
                android:textColor="#B0B0B0"
                android:textSize="14sp"
                android:layout_below="@id/dialogTitle"
                android:layout_marginTop="8dp" />

            <!-- Cancel / Allow -->
            <LinearLayout
                android:id="@+id/dialogButtons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end"
                android:layout_below="@id/dialogDesc"
                android:layout_marginTop="16dp">

                <Button
                    android:id="@+id/btnCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="CANCEL"
                    android:textColor="#86A1FF"
                    android:background="@android:color/transparent" />

                <Button
                    android:id="@+id/btnAllow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ALLOW"
                    android:textColor="#86A1FF"
                    android:background="@android:color/transparent" />
            </LinearLayout>

            <!-- Step 2 Circle -->
            <TextView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:text="2"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:textSize="16sp"
                android:gravity="center"
                android:background="@drawable/circle_orange"
                android:layout_alignParentEnd="true"
                android:layout_alignTop="@id/dialogButtons"
                android:layout_marginEnd="8dp"
                android:layout_marginTop="-16dp"
                android:elevation="10dp" />

            <!-- Finger pointing to ALLOW button - Touching ALLOW text -->
            <TextView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="👆"
                android:textSize="36sp"
                android:gravity="center"
                android:layout_alignParentEnd="true"
                android:layout_below="@id/dialogButtons"
                android:layout_marginEnd="18dp"
                android:layout_marginTop="-40dp"
                android:elevation="999dp"
                android:textColor="#FFFFFF"
                android:shadowColor="#000000"
                android:shadowDx="4"
                android:shadowDy="4"
                android:shadowRadius="8"
                android:background="@android:color/transparent" />

        </RelativeLayout>



        <!-- Use this Folder Button with Step 1 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/btnUseFolder"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:backgroundTint="#C5D8FF"
                android:text="Use this Folder"
                android:textColor="#000000"
                android:textStyle="bold" />

            <!-- Step 1 Circle -->
            <TextView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:text="1"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:textSize="16sp"
                android:gravity="center"
                android:background="@drawable/circle_orange"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:elevation="10dp" />

            <!-- Finger pointing to Use this Folder button - Positioned closer to button -->
            <TextView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="👆"
                android:textSize="36sp"
                android:gravity="center"
                android:layout_centerHorizontal="true"
                android:layout_below="@id/btnUseFolder"
                android:layout_marginTop="-30dp"
                android:elevation="999dp"
                android:textColor="#FFFFFF"
                android:shadowColor="#000000"
                android:shadowDx="4"
                android:shadowDy="4"
                android:shadowRadius="8"
                android:background="@android:color/transparent" />

        </RelativeLayout>

    </LinearLayout>

    <!-- Instructions outside card - Top Left -->
    <LinearLayout
        android:id="@+id/instructionsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_below="@id/mainContainer"
        android:layout_marginTop="24dp"
        android:layout_above="@+id/bottomButtons"
        android:gravity="start"
        android:layout_marginStart="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Grant Folder Access"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1️⃣ Tap &quot;USE THIS FOLDER&quot;"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2️⃣ Tap &quot;ALLOW&quot; in the popup"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Bottom Button outside card - Only CONTINUE -->
    <LinearLayout
        android:id="@+id/bottomButtons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="24dp">

        <Button
            android:id="@+id/button_allow_access"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:text="CONTINUE"
            android:backgroundTint="#00C686"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:paddingLeft="24dp"
            android:paddingRight="24dp" />
    </LinearLayout>
</RelativeLayout>

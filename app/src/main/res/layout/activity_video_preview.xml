<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".ui.videos.VideoPreviewActivity">

    <!-- Top Header with Back Button and Video ID -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp"
        android:background="#80000000"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/button_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back_24"
            android:tint="@android:color/white"
            android:contentDescription="@string/back_button" />

        <TextView
            android:id="@+id/text_video_id"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="Video"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="start|center_vertical" />

    </LinearLayout>

    <!-- Fixed Height Container for Consistent Sizing -->
    <FrameLayout
        android:id="@+id/media_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        app:layout_constraintTop_toBottomOf="@+id/header_layout"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout_actions_video"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Media3 PlayerView with Custom Controls -->
        <androidx.media3.ui.PlayerView
            android:id="@+id/video_view_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            app:use_controller="false"
            app:resize_mode="fit" />

        <!-- Custom Play/Pause Button - Centered -->
        <ImageButton
            android:id="@+id/btn_play_pause"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_gravity="center"
            android:background="@drawable/custom_play_button_background"
            android:src="@drawable/ic_play_arrow_white_48dp"
            android:scaleType="centerInside"
            android:contentDescription="Play/Pause"
            android:elevation="8dp" />

        <!-- Custom Progress Bar at Bottom -->
        <LinearLayout
            android:id="@+id/bottom_controls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical"
            android:background="@drawable/custom_controls_background"
            android:visibility="visible">

            <TextView
                android:id="@+id/tv_current_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="00:00"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:fontFamily="sans-serif-medium" />

            <SeekBar
                android:id="@+id/seekbar_progress"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="12dp"
                android:progressTint="#00C686"
                android:thumbTint="#00C686"
                android:progressBackgroundTint="#40FFFFFF" />

            <TextView
                android:id="@+id/tv_total_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="00:00"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:fontFamily="sans-serif-medium" />
        </LinearLayout>

        <!-- Loading Indicator -->
        <ProgressBar
            android:id="@+id/loading_indicator"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:indeterminateTint="#00C686"
            android:visibility="gone" />



    </FrameLayout>

    <!-- Bottom Actions with Fixed Height -->
    <LinearLayout
        android:id="@+id/linearLayout_actions_video"
        android:layout_width="0dp"
        android:layout_height="80dp"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="#80000000"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/button_download_preview_video"
            style="@style/Widget.MaterialComponents.Button.TextButton.Icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/download"
            android:textColor="@android:color/white"
            app:icon="@android:drawable/stat_sys_download"
            app:iconTint="@android:color/white" />

        <Button
            android:id="@+id/button_share_preview_video"
            style="@style/Widget.MaterialComponents.Button.TextButton.Icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:text="@string/share"
            android:textColor="@android:color/white"
            app:icon="@android:drawable/ic_menu_share"
            app:iconTint="@android:color/white" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background"
    android:gravity="center">

    <!-- Update Icon -->
    <ImageView
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:src="@drawable/ic_update"
        android:layout_marginBottom="16dp"
        android:tint="@color/colorPrimary" />

    <!-- Title -->
    <TextView
        android:id="@+id/update_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Update Required"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:layout_marginBottom="12dp"
        android:gravity="center" />

    <!-- Message -->
    <TextView
        android:id="@+id/update_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="A new version of SavePro is available. Please update to continue using the app."
        android:textSize="16sp"
        android:textColor="@color/white"
        android:layout_marginBottom="24dp"
        android:gravity="center"
        android:lineSpacingExtra="4dp" />

    <!-- Buttons Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- Exit App Button -->
        <Button
            android:id="@+id/btn_exit_app"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="EXIT APP"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:background="@drawable/button_exit_background"
            android:layout_marginEnd="8dp"
            android:elevation="2dp" />

        <!-- Update Now Button -->
        <Button
            android:id="@+id/btn_update_now"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="UPDATE NOW"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:background="@drawable/button_update_background"
            android:layout_marginStart="8dp"
            android:elevation="2dp" />

    </LinearLayout>

</LinearLayout>

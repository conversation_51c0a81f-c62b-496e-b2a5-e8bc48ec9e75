<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipe_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.facebook.shimmer.ShimmerFrameLayout
            android:id="@+id/shimmer_view_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:shimmer_auto_start="false"
            app:shimmer_base_alpha="0.8"
            app:shimmer_highlight_alpha="1.0"
            app:shimmer_direction="left_to_right"
            app:shimmer_duration="1000"
            app:shimmer_repeat_count="-1"
            app:shimmer_repeat_delay="300"
            app:shimmer_shape="linear"
            app:shimmer_tilt="0"
            android:visibility="gone">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/shimmer_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/grid_padding"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="@integer/grid_span_count_default" />

        </com.facebook.shimmer.ShimmerFrameLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:padding="@dimen/grid_padding"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="@integer/grid_span_count_default"
            tools:listitem="@layout/item_media"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/text_view_no_media"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_media_found"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
            android:textColor="@android:color/white"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

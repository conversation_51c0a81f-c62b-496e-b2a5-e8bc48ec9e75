<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/settings_item_background">

    <!-- Main content container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp"
        android:minHeight="56dp">

        <!-- Icon -->
        <ImageView
            android:id="@+id/item_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:tint="@android:color/white"
            android:src="@android:drawable/ic_menu_info_details" />

        <!-- Text -->
        <TextView
            android:id="@+id/item_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:text="Settings Item" />

        <!-- Arrow indicator -->
        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@android:drawable/ic_menu_more"
            android:tint="@color/settings_arrow_color"
            android:rotation="270" />

    </LinearLayout>

    <!-- Border/Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/settings_divider_color"
        android:layout_marginStart="56dp" />

</LinearLayout>

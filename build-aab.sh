#!/bin/bash

# Build AAB for Status Saver App
# Using existing keystore configuration

echo "📦 Building AAB for Status Saver App..."
echo "Using existing keystore configuration"
echo ""

# Check if keystore exists
if [ ! -f "app/release-key.jks" ]; then
    echo "❌ Keystore file not found: app/release-key.jks"
    echo "Please ensure the keystore file is in the correct location."
    exit 1
fi

echo "✅ Keystore found: app/release-key.jks"
echo "🔑 Using keystore configuration:"
echo "   - RELEASE_STORE_FILE=release-key.jks"
echo "   - RELEASE_KEY_ALIAS=release-key"
echo "   - RELEASE_STORE_PASSWORD=ksappservice123"
echo "   - RELEASE_KEY_PASSWORD=ksappservice123"
echo ""

echo "🧹 Cleaning previous builds..."
./gradlew clean

if [ $? -ne 0 ]; then
    echo "❌ Clean failed"
    exit 1
fi

echo "✅ Clean completed successfully!"
echo ""

echo "📦 Building AAB (Android App Bundle)..."
./gradlew bundleRelease

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ AAB built successfully!"
    echo "📍 Location: app/build/outputs/bundle/release/app-release.aab"
    echo ""
    echo "📊 Build Information:"
    echo "   - App ID: com.ks.app.service.statussaver"
    echo "   - Version: 1.1 (Code: 2)"
    echo "   - Keystore: release-key.jks"
    echo "   - Key Alias: release-key"
    echo ""
    echo "🚀 Ready for Google Play Store upload!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Upload app-release.aab to Google Play Console"
    echo "2. Fill in app details and descriptions"
    echo "3. Set up store listing with screenshots"
    echo "4. Configure pricing and distribution"
    echo "5. Submit for review"
    echo ""
else
    echo "❌ Failed to build AAB"
    echo "Please check the error messages above."
    echo ""
    echo "🔍 Troubleshooting:"
    echo "- Verify keystore file exists: app/release-key.jks"
    echo "- Check gradle.properties has correct values"
    echo "- Ensure all dependencies are downloaded"
fi

# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
org.gradle.parallel=true
# Enable Gradle build cache for faster builds
org.gradle.caching=true
# Enable configuration cache (experimental but faster)
org.gradle.configuration-cache=true
# Enable file system watching for faster incremental builds
org.gradle.vfs.watch=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
# Enable R8 full mode for better optimization
android.enableR8.fullMode=true
# Suppress compileSdk warning for AGP 8.7.3 with compileSdk 35 (Android 15 targeting)
android.suppressUnsupportedCompileSdk=34,35

# Enable Android 15 compatibility features
android.enableJetifier=true
android.useAndroidX=true

# Android 15 specific optimizations
android.enableR8.fullMode=true
# Disable unnecessary features for faster builds (removed deprecated option)
# Optimize for faster builds (removed deprecated option)
#KEYSTORE_FILE=app/my-release-key.jks
#KEYSTORE_PASSWORD=95977679
#KEY_ALIAS=com.ks.app.service.statussaver
#KEY_PASSWORD=95977679


RELEASE_STORE_FILE=release-key.jks
RELEASE_KEY_ALIAS=release-key
RELEASE_STORE_PASSWORD=ksappservice123
RELEASE_KEY_PASSWORD=ksappservice123

# Set Java home to use Java 17 (required for AGP 8.7.3+)
org.gradle.java.home=/opt/homebrew/Cellar/openjdk@17/17.0.16/libexec/openjdk.jdk/Contents/Home
